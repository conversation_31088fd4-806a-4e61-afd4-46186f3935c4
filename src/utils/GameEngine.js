/**
 * Teepana Games World - Core Game Engine
 * Provides base functionality for all games including canvas rendering,
 * input handling, scoring, and localStorage persistence
 */

class GameEngine {
    constructor(canvasId, options = {}) {
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
        
        // Game state
        this.isRunning = false;
        this.isPaused = false;
        this.gameLoop = null;
        this.lastTime = 0;
        this.deltaTime = 0;
        
        // Configuration
        this.config = {
            width: options.width || 800,
            height: options.height || 600,
            backgroundColor: options.backgroundColor || '#000000',
            targetFPS: options.targetFPS || 60,
            ...options
        };
        
        // Input handling
        this.keys = {};
        this.mouse = { x: 0, y: 0, pressed: false };
        this.touches = [];
        
        // Scoring system
        this.score = 0;
        this.highScore = 0;
        this.level = 1;
        this.lives = 3;
        
        // Game name for localStorage
        this.gameName = options.gameName || 'teepana-game';
        
        // Initialize
        this.init();
    }
    
    init() {
        this.setupCanvas();
        this.setupInput();
        this.loadHighScore();
        this.setupEventListeners();
    }
    
    setupCanvas() {
        this.canvas.width = this.config.width;
        this.canvas.height = this.config.height;
        this.canvas.style.maxWidth = '100%';
        this.canvas.style.height = 'auto';
        
        // Enable image smoothing for better graphics
        this.ctx.imageSmoothingEnabled = true;
        this.ctx.imageSmoothingQuality = 'high';
    }
    
    setupInput() {
        // Keyboard input
        document.addEventListener('keydown', (e) => {
            this.keys[e.code] = true;
            this.onKeyDown(e);
        });
        
        document.addEventListener('keyup', (e) => {
            this.keys[e.code] = false;
            this.onKeyUp(e);
        });
        
        // Mouse input
        this.canvas.addEventListener('mousedown', (e) => {
            const rect = this.canvas.getBoundingClientRect();
            this.mouse.x = (e.clientX - rect.left) * (this.canvas.width / rect.width);
            this.mouse.y = (e.clientY - rect.top) * (this.canvas.height / rect.height);
            this.mouse.pressed = true;
            this.onMouseDown(e);
        });
        
        this.canvas.addEventListener('mouseup', (e) => {
            this.mouse.pressed = false;
            this.onMouseUp(e);
        });
        
        this.canvas.addEventListener('mousemove', (e) => {
            const rect = this.canvas.getBoundingClientRect();
            this.mouse.x = (e.clientX - rect.left) * (this.canvas.width / rect.width);
            this.mouse.y = (e.clientY - rect.top) * (this.canvas.height / rect.height);
            this.onMouseMove(e);
        });
        
        // Touch input for mobile
        this.canvas.addEventListener('touchstart', (e) => {
            e.preventDefault();
            const rect = this.canvas.getBoundingClientRect();
            this.touches = Array.from(e.touches).map(touch => ({
                x: (touch.clientX - rect.left) * (this.canvas.width / rect.width),
                y: (touch.clientY - rect.top) * (this.canvas.height / rect.height),
                id: touch.identifier
            }));
            this.onTouchStart(e);
        });
        
        this.canvas.addEventListener('touchend', (e) => {
            e.preventDefault();
            this.touches = [];
            this.onTouchEnd(e);
        });
        
        this.canvas.addEventListener('touchmove', (e) => {
            e.preventDefault();
            const rect = this.canvas.getBoundingClientRect();
            this.touches = Array.from(e.touches).map(touch => ({
                x: (touch.clientX - rect.left) * (this.canvas.width / rect.width),
                y: (touch.clientY - rect.top) * (this.canvas.height / rect.height),
                id: touch.identifier
            }));
            this.onTouchMove(e);
        });
    }
    
    setupEventListeners() {
        // Pause when window loses focus
        window.addEventListener('blur', () => {
            if (this.isRunning) {
                this.pause();
            }
        });
        
        // Handle visibility change
        document.addEventListener('visibilitychange', () => {
            if (document.hidden && this.isRunning) {
                this.pause();
            }
        });
    }
    
    // Game loop
    start() {
        if (!this.isRunning) {
            this.isRunning = true;
            this.isPaused = false;
            this.lastTime = performance.now();
            this.gameLoop = requestAnimationFrame((time) => this.loop(time));
            this.onStart();
        }
    }
    
    stop() {
        this.isRunning = false;
        this.isPaused = false;
        if (this.gameLoop) {
            cancelAnimationFrame(this.gameLoop);
            this.gameLoop = null;
        }
        this.onStop();
    }
    
    pause() {
        if (this.isRunning && !this.isPaused) {
            this.isPaused = true;
            this.onPause();
        }
    }
    
    resume() {
        if (this.isRunning && this.isPaused) {
            this.isPaused = false;
            this.lastTime = performance.now();
            this.gameLoop = requestAnimationFrame((time) => this.loop(time));
            this.onResume();
        }
    }
    
    loop(currentTime) {
        if (!this.isRunning || this.isPaused) return;
        
        this.deltaTime = (currentTime - this.lastTime) / 1000;
        this.lastTime = currentTime;
        
        // Cap delta time to prevent large jumps
        this.deltaTime = Math.min(this.deltaTime, 1/30);
        
        this.update(this.deltaTime);
        this.render();
        
        this.gameLoop = requestAnimationFrame((time) => this.loop(time));
    }
    
    // Rendering utilities
    clear() {
        this.ctx.fillStyle = this.config.backgroundColor;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }
    
    drawText(text, x, y, options = {}) {
        const {
            font = '20px Orbitron',
            color = '#FFD700',
            align = 'left',
            baseline = 'top',
            shadow = true
        } = options;
        
        this.ctx.save();
        this.ctx.font = font;
        this.ctx.fillStyle = color;
        this.ctx.textAlign = align;
        this.ctx.textBaseline = baseline;
        
        if (shadow) {
            this.ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
            this.ctx.shadowBlur = 4;
            this.ctx.shadowOffsetX = 2;
            this.ctx.shadowOffsetY = 2;
        }
        
        this.ctx.fillText(text, x, y);
        this.ctx.restore();
    }
    
    drawRect(x, y, width, height, color = '#FFD700') {
        this.ctx.fillStyle = color;
        this.ctx.fillRect(x, y, width, height);
    }
    
    drawCircle(x, y, radius, color = '#FFD700') {
        this.ctx.beginPath();
        this.ctx.arc(x, y, radius, 0, Math.PI * 2);
        this.ctx.fillStyle = color;
        this.ctx.fill();
    }
    
    // Scoring system
    addScore(points) {
        this.score += points;
        if (this.score > this.highScore) {
            this.highScore = this.score;
            this.saveHighScore();
        }
        this.onScoreUpdate(this.score, points);
    }
    
    resetScore() {
        this.score = 0;
        this.level = 1;
        this.lives = 3;
    }
    
    loadHighScore() {
        const saved = localStorage.getItem(`teepana-${this.gameName}-highscore`);
        this.highScore = saved ? parseInt(saved) : 0;
    }
    
    saveHighScore() {
        localStorage.setItem(`teepana-${this.gameName}-highscore`, this.highScore.toString());
    }
    
    // Utility methods
    random(min, max) {
        return Math.random() * (max - min) + min;
    }
    
    randomInt(min, max) {
        return Math.floor(this.random(min, max + 1));
    }
    
    distance(x1, y1, x2, y2) {
        return Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);
    }
    
    clamp(value, min, max) {
        return Math.min(Math.max(value, min), max);
    }
    
    // Event handlers (to be overridden by games)
    onStart() {}
    onStop() {}
    onPause() {}
    onResume() {}
    onKeyDown(event) {}
    onKeyUp(event) {}
    onMouseDown(event) {}
    onMouseUp(event) {}
    onMouseMove(event) {}
    onTouchStart(event) {}
    onTouchEnd(event) {}
    onTouchMove(event) {}
    onScoreUpdate(score, points) {}
    
    // Main game methods (to be overridden by games)
    update(deltaTime) {
        // Override this method in your game
    }
    
    render() {
        this.clear();
        // Override this method in your game
    }
}

export default GameEngine;
