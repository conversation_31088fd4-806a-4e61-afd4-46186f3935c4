/**
 * Teepana Games World - Score Management System
 * Handles high scores, leaderboards, and player statistics
 */

class ScoreManager {
    constructor() {
        this.storagePrefix = 'teepana-games';
        this.maxLeaderboardEntries = 10;
    }
    
    // High Score Management
    getHighScore(gameName) {
        const key = `${this.storagePrefix}-${gameName}-highscore`;
        return parseInt(localStorage.getItem(key)) || 0;
    }
    
    setHighScore(gameName, score) {
        const key = `${this.storagePrefix}-${gameName}-highscore`;
        const currentHigh = this.getHighScore(gameName);
        
        if (score > currentHigh) {
            localStorage.setItem(key, score.toString());
            return true; // New high score!
        }
        return false;
    }
    
    // Leaderboard Management
    getLeaderboard(gameName) {
        const key = `${this.storagePrefix}-${gameName}-leaderboard`;
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : [];
    }
    
    addToLeaderboard(gameName, playerName, score, level = 1) {
        const leaderboard = this.getLeaderboard(gameName);
        const entry = {
            name: playerName || 'Anonymous',
            score: score,
            level: level,
            date: new Date().toISOString(),
            timestamp: Date.now()
        };
        
        leaderboard.push(entry);
        leaderboard.sort((a, b) => b.score - a.score);
        
        // Keep only top entries
        const trimmed = leaderboard.slice(0, this.maxLeaderboardEntries);
        
        const key = `${this.storagePrefix}-${gameName}-leaderboard`;
        localStorage.setItem(key, JSON.stringify(trimmed));
        
        // Return the rank (1-based)
        return trimmed.findIndex(e => e.timestamp === entry.timestamp) + 1;
    }
    
    // Player Statistics
    getPlayerStats(gameName) {
        const key = `${this.storagePrefix}-${gameName}-stats`;
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : {
            gamesPlayed: 0,
            totalScore: 0,
            averageScore: 0,
            bestLevel: 0,
            totalPlayTime: 0,
            achievements: []
        };
    }
    
    updatePlayerStats(gameName, gameData) {
        const stats = this.getPlayerStats(gameName);
        
        stats.gamesPlayed += 1;
        stats.totalScore += gameData.score || 0;
        stats.averageScore = Math.round(stats.totalScore / stats.gamesPlayed);
        stats.bestLevel = Math.max(stats.bestLevel, gameData.level || 0);
        stats.totalPlayTime += gameData.playTime || 0;
        
        // Check for achievements
        this.checkAchievements(gameName, stats, gameData);
        
        const key = `${this.storagePrefix}-${gameName}-stats`;
        localStorage.setItem(key, JSON.stringify(stats));
        
        return stats;
    }
    
    // Achievement System
    checkAchievements(gameName, stats, gameData) {
        const achievements = [
            {
                id: 'first-game',
                name: 'First Steps',
                description: 'Play your first game',
                condition: () => stats.gamesPlayed === 1
            },
            {
                id: 'score-1000',
                name: 'Rising Star',
                description: 'Score 1,000 points',
                condition: () => gameData.score >= 1000
            },
            {
                id: 'score-5000',
                name: 'Desert Master',
                description: 'Score 5,000 points',
                condition: () => gameData.score >= 5000
            },
            {
                id: 'score-10000',
                name: 'Moroccan Legend',
                description: 'Score 10,000 points',
                condition: () => gameData.score >= 10000
            },
            {
                id: 'games-10',
                name: 'Dedicated Player',
                description: 'Play 10 games',
                condition: () => stats.gamesPlayed >= 10
            },
            {
                id: 'games-50',
                name: 'Gaming Enthusiast',
                description: 'Play 50 games',
                condition: () => stats.gamesPlayed >= 50
            },
            {
                id: 'level-10',
                name: 'Level Master',
                description: 'Reach level 10',
                condition: () => gameData.level >= 10
            },
            {
                id: 'playtime-hour',
                name: 'Time Traveler',
                description: 'Play for 1 hour total',
                condition: () => stats.totalPlayTime >= 3600000 // 1 hour in ms
            }
        ];
        
        achievements.forEach(achievement => {
            if (!stats.achievements.includes(achievement.id) && achievement.condition()) {
                stats.achievements.push(achievement.id);
                this.showAchievementNotification(achievement);
            }
        });
    }
    
    showAchievementNotification(achievement) {
        // Create achievement notification
        const notification = document.createElement('div');
        notification.className = 'achievement-notification';
        notification.innerHTML = `
            <div class="achievement-content">
                <div class="achievement-icon">🏆</div>
                <div class="achievement-text">
                    <div class="achievement-title">Achievement Unlocked!</div>
                    <div class="achievement-name">${achievement.name}</div>
                    <div class="achievement-desc">${achievement.description}</div>
                </div>
            </div>
        `;
        
        // Add styles
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            color: #000;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
            z-index: 10000;
            transform: translateX(400px);
            transition: transform 0.5s ease;
            max-width: 300px;
            font-family: 'Orbitron', sans-serif;
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Animate out and remove
        setTimeout(() => {
            notification.style.transform = 'translateX(400px)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 500);
        }, 4000);
    }
    
    // Global Statistics
    getGlobalStats() {
        const games = [
            'desert-snake', 'marrakech-tetris', 'souq-runner', 'camel-caravan',
            'atlas-invaders', 'berber-puzzle', 'minaret-defense', 'oasis-pong',
            'henna-tracer', 'tagine-chef', 'djemaa-memory', 'carpet-weaver',
            'spice-trader', 'desert-mirage', 'andalusi-garden', 'marrakech-parkour',
            'calligraphy-master', 'lantern-lighter', 'casbah-navigator', 'atlas-echo'
        ];
        
        let totalGames = 0;
        let totalScore = 0;
        let totalPlayTime = 0;
        let totalAchievements = 0;
        
        games.forEach(game => {
            const stats = this.getPlayerStats(game);
            totalGames += stats.gamesPlayed;
            totalScore += stats.totalScore;
            totalPlayTime += stats.totalPlayTime;
            totalAchievements += stats.achievements.length;
        });
        
        return {
            totalGames,
            totalScore,
            totalPlayTime,
            totalAchievements,
            averageScore: totalGames > 0 ? Math.round(totalScore / totalGames) : 0,
            gamesCompleted: games.filter(game => this.getPlayerStats(game).gamesPlayed > 0).length
        };
    }
    
    // Export/Import Data
    exportData() {
        const data = {};
        const keys = Object.keys(localStorage).filter(key => key.startsWith(this.storagePrefix));
        
        keys.forEach(key => {
            data[key] = localStorage.getItem(key);
        });
        
        return JSON.stringify(data, null, 2);
    }
    
    importData(jsonData) {
        try {
            const data = JSON.parse(jsonData);
            Object.keys(data).forEach(key => {
                if (key.startsWith(this.storagePrefix)) {
                    localStorage.setItem(key, data[key]);
                }
            });
            return true;
        } catch (error) {
            console.error('Failed to import data:', error);
            return false;
        }
    }
    
    // Clear all data
    clearAllData() {
        const keys = Object.keys(localStorage).filter(key => key.startsWith(this.storagePrefix));
        keys.forEach(key => localStorage.removeItem(key));
    }
    
    clearGameData(gameName) {
        const keys = Object.keys(localStorage).filter(key => 
            key.startsWith(`${this.storagePrefix}-${gameName}`)
        );
        keys.forEach(key => localStorage.removeItem(key));
    }
}

// Create global instance
const scoreManager = new ScoreManager();

export default scoreManager;
