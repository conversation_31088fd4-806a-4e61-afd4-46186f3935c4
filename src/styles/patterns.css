/* Teepana Games World - Moroccan Patterns & 80s Effects */

/* Moroccan Geometric Patterns */
.pattern-zellige {
  background-image: 
    radial-gradient(circle at 25% 25%, var(--color-terracotta) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, var(--color-gold) 2px, transparent 2px);
  background-size: 20px 20px;
  background-position: 0 0, 10px 10px;
}

.pattern-islamic {
  background-image: 
    linear-gradient(45deg, var(--color-deep-blue) 25%, transparent 25%),
    linear-gradient(-45deg, var(--color-deep-blue) 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, var(--color-terracotta) 75%),
    linear-gradient(-45deg, transparent 75%, var(--color-terracotta) 75%);
  background-size: 30px 30px;
  background-position: 0 0, 0 15px, 15px -15px, -15px 0px;
}

.pattern-berber {
  background-image: 
    repeating-linear-gradient(
      45deg,
      var(--color-gold) 0px,
      var(--color-gold) 2px,
      transparent 2px,
      transparent 10px
    ),
    repeating-linear-gradient(
      -45deg,
      var(--color-red) 0px,
      var(--color-red) 2px,
      transparent 2px,
      transparent 10px
    );
}

.pattern-carpet {
  background-image: 
    radial-gradient(ellipse at center, var(--color-terracotta) 20%, transparent 20%),
    linear-gradient(0deg, var(--color-deep-blue) 50%, var(--color-gold) 50%);
  background-size: 40px 40px, 20px 20px;
}

/* 80s Glitch Effects */
@keyframes glitch {
  0% {
    transform: translate(0);
  }
  20% {
    transform: translate(-2px, 2px);
  }
  40% {
    transform: translate(-2px, -2px);
  }
  60% {
    transform: translate(2px, 2px);
  }
  80% {
    transform: translate(2px, -2px);
  }
  100% {
    transform: translate(0);
  }
}

@keyframes glitch-text {
  0% {
    text-shadow: 
      0.05em 0 0 var(--glitch-color-1),
      -0.05em -0.025em 0 var(--glitch-color-2),
      0.025em 0.05em 0 var(--glitch-color-3);
  }
  15% {
    text-shadow: 
      0.05em 0 0 var(--glitch-color-1),
      -0.05em -0.025em 0 var(--glitch-color-2),
      0.025em 0.05em 0 var(--glitch-color-3);
  }
  16% {
    text-shadow: 
      -0.05em -0.025em 0 var(--glitch-color-1),
      0.025em 0.025em 0 var(--glitch-color-2),
      -0.05em -0.05em 0 var(--glitch-color-3);
  }
  49% {
    text-shadow: 
      -0.05em -0.025em 0 var(--glitch-color-1),
      0.025em 0.025em 0 var(--glitch-color-2),
      -0.05em -0.05em 0 var(--glitch-color-3);
  }
  50% {
    text-shadow: 
      0.025em 0.05em 0 var(--glitch-color-1),
      0.05em 0 0 var(--glitch-color-2),
      0 -0.05em 0 var(--glitch-color-3);
  }
  99% {
    text-shadow: 
      0.025em 0.05em 0 var(--glitch-color-1),
      0.05em 0 0 var(--glitch-color-2),
      0 -0.05em 0 var(--glitch-color-3);
  }
  100% {
    text-shadow: 
      0.05em 0 0 var(--glitch-color-1),
      -0.05em -0.025em 0 var(--glitch-color-2),
      0.025em 0.05em 0 var(--glitch-color-3);
  }
}

@keyframes neon-glow {
  0%, 100% {
    text-shadow: 
      0 0 5px currentColor,
      0 0 10px currentColor,
      0 0 15px currentColor,
      0 0 20px var(--color-neon-cyan);
  }
  50% {
    text-shadow: 
      0 0 2px currentColor,
      0 0 5px currentColor,
      0 0 8px currentColor,
      0 0 12px var(--color-neon-cyan);
  }
}

@keyframes scan-lines {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 0 20px;
  }
}

/* Effect Classes */
.glitch {
  animation: glitch 0.3s infinite;
}

.glitch-text {
  animation: glitch-text 2s infinite;
}

.neon-glow {
  animation: neon-glow 2s ease-in-out infinite alternate;
}

.scan-lines {
  position: relative;
}

.scan-lines::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    repeating-linear-gradient(
      0deg,
      rgba(0, 255, 255, 0.03) 0px,
      rgba(0, 255, 255, 0.03) 1px,
      transparent 1px,
      transparent 2px
    );
  animation: scan-lines 0.1s linear infinite;
  pointer-events: none;
}

/* Retro Grid */
.retro-grid {
  background-image: 
    linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Moroccan Ornaments */
.ornament-corner {
  position: relative;
}

.ornament-corner::before,
.ornament-corner::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-gold);
}

.ornament-corner::before {
  top: -10px;
  left: -10px;
  border-right: none;
  border-bottom: none;
}

.ornament-corner::after {
  bottom: -10px;
  right: -10px;
  border-left: none;
  border-top: none;
}

/* Particle Effects */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.floating {
  animation: float 3s ease-in-out infinite;
}

/* Moroccan Border */
.moroccan-border {
  border: 3px solid;
  border-image: linear-gradient(45deg, var(--color-gold), var(--color-terracotta), var(--color-deep-blue), var(--color-red)) 1;
  position: relative;
}

.moroccan-border::before {
  content: '';
  position: absolute;
  top: -6px;
  left: -6px;
  right: -6px;
  bottom: -6px;
  border: 1px solid var(--color-gold);
  opacity: 0.5;
}

/* Game Canvas Styling */
.game-canvas {
  border: 3px solid var(--color-gold);
  border-radius: var(--radius-lg);
  box-shadow: 
    0 0 20px rgba(255, 215, 0, 0.3),
    inset 0 0 20px rgba(0, 0, 0, 0.2);
  background: var(--color-black);
}

/* Loading Animation */
@keyframes loading-dots {
  0%, 20% {
    color: var(--color-gold);
    transform: scale(1);
  }
  50% {
    color: var(--color-gold-light);
    transform: scale(1.2);
  }
  100% {
    color: var(--color-gold);
    transform: scale(1);
  }
}

.loading-dots span {
  animation: loading-dots 1.4s infinite ease-in-out;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }
.loading-dots span:nth-child(3) { animation-delay: 0s; }
