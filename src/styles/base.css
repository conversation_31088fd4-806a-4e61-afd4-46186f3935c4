/* Teepana Games World - Base Styles */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&family=Amiri:wght@400;700&display=swap');
@import './variables.css';

/* Reset and Base */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-secondary);
  font-weight: 400;
  line-height: 1.6;
  color: var(--color-white);
  background: linear-gradient(135deg, var(--color-deep-blue-dark) 0%, var(--color-terracotta-dark) 100%);
  min-height: 100vh;
  overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-primary);
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: var(--space-4);
}

h1 {
  font-size: var(--text-5xl);
  font-weight: 900;
}

h2 {
  font-size: var(--text-4xl);
}

h3 {
  font-size: var(--text-3xl);
}

h4 {
  font-size: var(--text-2xl);
}

h5 {
  font-size: var(--text-xl);
}

h6 {
  font-size: var(--text-lg);
}

p {
  margin-bottom: var(--space-4);
}

a {
  color: var(--color-gold);
  text-decoration: none;
  transition: var(--transition-fast);
}

a:hover {
  color: var(--color-gold-light);
  text-shadow: 0 0 10px var(--color-gold);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-3) var(--space-6);
  font-family: var(--font-primary);
  font-weight: 600;
  font-size: var(--text-base);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border: 2px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: var(--transition-normal);
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.btn:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: var(--transition-slow);
}

.btn:hover:before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(45deg, var(--color-gold), var(--color-gold-light));
  color: var(--color-black);
  border-color: var(--color-gold);
}

.btn-primary:hover {
  background: linear-gradient(45deg, var(--color-gold-light), var(--color-gold));
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(255, 215, 0, 0.3);
}

.btn-secondary {
  background: transparent;
  color: var(--color-white);
  border-color: var(--color-white);
}

.btn-secondary:hover {
  background: var(--color-white);
  color: var(--color-deep-blue);
}

.btn-game {
  background: linear-gradient(45deg, var(--color-terracotta), var(--color-red));
  color: var(--color-white);
  border-color: var(--color-terracotta);
  min-width: 200px;
  padding: var(--space-4) var(--space-6);
}

.btn-game:hover {
  background: linear-gradient(45deg, var(--color-red), var(--color-terracotta));
  transform: translateY(-3px);
  box-shadow: 0 15px 30px rgba(220, 38, 38, 0.4);
}

/* Cards */
.card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  transition: var(--transition-normal);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-2xl);
  border-color: var(--color-gold);
}

/* Utilities */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.text-center {
  text-align: center;
}

.text-gradient {
  background: linear-gradient(45deg, var(--color-gold), var(--color-gold-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hidden {
  display: none;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }
  
  .container {
    padding: 0 var(--space-3);
  }
  
  h1 {
    font-size: var(--text-4xl);
  }
  
  h2 {
    font-size: var(--text-3xl);
  }
  
  .btn {
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-sm);
  }
  
  .btn-game {
    min-width: 150px;
    padding: var(--space-3) var(--space-4);
  }
}
