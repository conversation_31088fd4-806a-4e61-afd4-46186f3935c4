/* Teepana Games World - Main Stylesheet */
@import './variables.css';
@import './base.css';
@import './patterns.css';

/* Landing Page Specific Styles */
.hero {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(220, 38, 38, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 215, 0, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(30, 58, 138, 0.4) 0%, transparent 50%);
  z-index: -1;
}

.hero-title {
  font-size: clamp(2rem, 8vw, 6rem);
  margin-bottom: var(--space-6);
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.hero-subtitle {
  font-size: clamp(1rem, 3vw, 1.5rem);
  margin-bottom: var(--space-8);
  opacity: 0.9;
  font-family: var(--font-arabic);
}

.games-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-6);
  padding: var(--space-8) 0;
}

.game-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  border: 2px solid rgba(255, 215, 0, 0.3);
  border-radius: var(--radius-2xl);
  padding: var(--space-6);
  text-align: center;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.game-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 215, 0, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform var(--transition-slow);
}

.game-card:hover::before {
  transform: translateX(100%);
}

.game-card:hover {
  transform: translateY(-10px) scale(1.02);
  border-color: var(--color-gold);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 30px rgba(255, 215, 0, 0.4);
}

.game-icon {
  font-size: var(--text-5xl);
  margin-bottom: var(--space-4);
  display: block;
}

.game-title {
  font-size: var(--text-xl);
  margin-bottom: var(--space-3);
  color: var(--color-gold);
}

.game-description {
  font-size: var(--text-sm);
  opacity: 0.8;
  margin-bottom: var(--space-4);
  line-height: 1.5;
}

.game-category {
  display: inline-block;
  background: linear-gradient(45deg, var(--color-terracotta), var(--color-red));
  color: var(--color-white);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--space-4);
}

.navigation {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 215, 0, 0.3);
  z-index: var(--z-fixed);
  padding: var(--space-4) 0;
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-family: var(--font-primary);
  font-size: var(--text-2xl);
  font-weight: 900;
  color: var(--color-gold);
  text-decoration: none;
}

.nav-links {
  display: flex;
  list-style: none;
  gap: var(--space-6);
}

.nav-link {
  color: var(--color-white);
  font-weight: 500;
  transition: var(--transition-fast);
}

.nav-link:hover {
  color: var(--color-gold);
}

.footer {
  background: rgba(0, 0, 0, 0.8);
  padding: var(--space-8) 0;
  text-align: center;
  border-top: 1px solid rgba(255, 215, 0, 0.3);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-6);
}

.footer-section h3 {
  color: var(--color-gold);
  margin-bottom: var(--space-3);
}

.footer-section p,
.footer-section a {
  opacity: 0.8;
  font-size: var(--text-sm);
}

.social-links {
  display: flex;
  justify-content: center;
  gap: var(--space-4);
  margin-top: var(--space-4);
}

.social-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 215, 0, 0.1);
  border: 1px solid var(--color-gold);
  border-radius: var(--radius-full);
  color: var(--color-gold);
  transition: var(--transition-fast);
}

.social-link:hover {
  background: var(--color-gold);
  color: var(--color-black);
  transform: translateY(-2px);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .games-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
    padding: var(--space-6) 0;
  }
  
  .game-card {
    padding: var(--space-4);
  }
  
  .nav-links {
    display: none;
  }
  
  .hero {
    padding: var(--space-16) 0;
  }
  
  .hero-title {
    font-size: clamp(1.5rem, 6vw, 3rem);
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
}

/* Game Page Styles */
.game-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding-top: 80px;
}

.game-header {
  text-align: center;
  padding: var(--space-8) 0;
}

.game-area {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--space-4);
}

.game-controls {
  display: flex;
  justify-content: center;
  gap: var(--space-4);
  padding: var(--space-6) 0;
  flex-wrap: wrap;
}

.score-display {
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid var(--color-gold);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin: var(--space-4) 0;
  text-align: center;
}

.score-item {
  display: inline-block;
  margin: 0 var(--space-4);
}

.score-label {
  font-size: var(--text-sm);
  opacity: 0.8;
  display: block;
}

.score-value {
  font-family: var(--font-primary);
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--color-gold);
}
