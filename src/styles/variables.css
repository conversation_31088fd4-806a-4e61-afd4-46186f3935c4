/* Teepana Games World - CSS Variables */
/* Moroccan Color Palette */
:root {
  /* Primary Moroccan Colors */
  --color-terracotta: #D2691E;
  --color-terracotta-light: #E6A85C;
  --color-terracotta-dark: #A0522D;
  
  --color-deep-blue: #1E3A8A;
  --color-deep-blue-light: #3B82F6;
  --color-deep-blue-dark: #1E40AF;
  
  --color-gold: #FFD700;
  --color-gold-light: #FFED4E;
  --color-gold-dark: #B8860B;
  
  --color-red: #DC2626;
  --color-red-light: #EF4444;
  --color-red-dark: #B91C1C;
  
  /* Secondary Moroccan Colors */
  --color-sand: #F4E4BC;
  --color-sand-light: #FAF0E6;
  --color-sand-dark: #DEB887;
  
  --color-mint: #10B981;
  --color-mint-light: #34D399;
  --color-mint-dark: #059669;
  
  --color-purple: #7C3AED;
  --color-purple-light: #8B5CF6;
  --color-purple-dark: #6D28D9;
  
  /* Neutral Colors */
  --color-white: #FFFFFF;
  --color-black: #000000;
  --color-gray-100: #F3F4F6;
  --color-gray-200: #E5E7EB;
  --color-gray-300: #D1D5DB;
  --color-gray-400: #9CA3AF;
  --color-gray-500: #6B7280;
  --color-gray-600: #4B5563;
  --color-gray-700: #374151;
  --color-gray-800: #1F2937;
  --color-gray-900: #111827;
  
  /* 80s Retro Colors */
  --color-neon-pink: #FF1493;
  --color-neon-cyan: #00FFFF;
  --color-neon-green: #39FF14;
  --color-neon-purple: #BF00FF;
  --color-neon-orange: #FF4500;
  
  /* Typography */
  --font-primary: 'Orbitron', 'Arial Black', sans-serif;
  --font-secondary: 'Rajdhani', 'Arial', sans-serif;
  --font-arabic: 'Amiri', 'Times New Roman', serif;
  
  /* Font Sizes */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  --text-6xl: 3.75rem;
  
  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;
  
  /* Border Radius */
  --radius-sm: 0.125rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  
  /* Glitch Effects */
  --glitch-color-1: var(--color-neon-pink);
  --glitch-color-2: var(--color-neon-cyan);
  --glitch-color-3: var(--color-neon-green);
  
  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
  
  /* Z-Index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  
  /* Breakpoints (for reference in JS) */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}
