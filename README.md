# 🎮 Teepana Games World

**Moroccan 80s Retro Gaming Website**

A stunning collection of 20 playable games featuring authentic Moroccan/Marrakech aesthetics combined with nostalgic 80s retro glitchy game styling.

## 🌟 Features

- **20 Unique Games**: Mix of classic and innovative games with Moroccan themes
- **Moroccan Aesthetics**: Authentic color palettes, geometric patterns, and cultural elements
- **80s Retro Style**: Glitch effects, retro fonts, and nostalgic visual elements
- **Mobile-First Design**: Responsive and optimized for all devices
- **Persistent High Scores**: Local storage-based scoring system
- **Modular Architecture**: Clean, maintainable code structure

## 🎯 Game Collection

### Classic Games (Moroccan-themed)
1. **Desert Snake** - Snake game in Sahara setting
2. **Marrakech Tetris** - Tetris with Moroccan tile patterns
3. **Souq Runner** - Endless runner through bustling markets
4. **Camel Caravan** - Frogger-style desert crossing
5. **Atlas Invaders** - Space Invaders with flying carpets
6. **Berber Puzzle** - Match-3 with traditional jewelry
7. **Minaret Defense** - Tower defense with architecture
8. **Oasis Pong** - Classic Pong with palm trees

### Novel/Innovative Games
9. **Henna <PERSON>tern Tracer** - Draw traditional designs
10. **Tagine Chef** - Cooking rhythm game
11. **Dje<PERSON><PERSON> Memory** - <PERSON>s with drum beats
12. **Carpet Weaver** - Pattern-matching simulation
13. **Spice Trader** - Economic strategy mini-game
14. **Desert Mirage** - Optical illusion platformer
15. **Andalusi Garden** - Plant growth with Islamic geometry
16. **Marrakech Parkour** - Physics-based rooftop jumping
17. **Calligraphy Master** - Arabic script drawing
18. **Lantern Lighter** - Light-based geometric puzzles
19. **Casbah Navigator** - Maze through narrow alleyways
20. **Atlas Echo** - Sound-based timing game

## 🚀 Getting Started

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

## 📁 Project Structure

```
teepana-games-world/
├── src/
│   ├── components/     # Reusable UI components
│   ├── games/         # Individual game modules
│   ├── styles/        # CSS and styling system
│   ├── utils/         # Utility functions
│   └── assets/        # Images, sounds, fonts
├── games/             # Individual game HTML pages
├── public/            # Static assets
└── index.html         # Landing page
```

## 🎨 Design System

- **Colors**: Terracotta, deep blue, gold, red
- **Patterns**: Traditional Moroccan geometric designs
- **Typography**: 80s retro fonts with Arabic influences
- **Effects**: Glitch animations and particle systems

## 📱 Mobile Optimization

- Touch-friendly controls
- Responsive layouts
- Performance optimized
- Progressive Web App features

---

**Made with ❤️ for the Moroccan gaming community**
