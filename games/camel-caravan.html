<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🐪 Camel Caravan - Teepana Games World</title>
    <meta name="description" content="Guide your caravan safely across treacherous desert routes in this Frogger-style adventure.">
    
    <link rel="stylesheet" href="../src/styles/main.css">
    <style>
        .game-container {
            background: linear-gradient(135deg, #DEB887 0%, #D2691E 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }
        
        .game-canvas {
            border: 4px solid #FFD700;
            border-radius: 10px;
            box-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
            background: #F4E4BC;
        }
        
        .lives-display {
            display: flex;
            gap: 10px;
            margin: 15px 0;
            justify-content: center;
        }
        
        .life-icon {
            font-size: 2rem;
            transition: all 0.3s ease;
        }
        
        .life-icon.lost {
            opacity: 0.3;
            transform: scale(0.8);
        }
        
        .mobile-controls {
            display: none;
            grid-template-areas: 
                ". up ."
                "left . right"
                ". down .";
            gap: 10px;
            margin: 20px 0;
            max-width: 200px;
        }
        
        .mobile-btn {
            padding: 15px;
            background: rgba(255, 215, 0, 0.2);
            border: 2px solid #FFD700;
            border-radius: 8px;
            color: #FFD700;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .mobile-btn:hover, .mobile-btn:active {
            background: rgba(255, 215, 0, 0.4);
            transform: scale(0.95);
        }
        
        .mobile-btn.up { grid-area: up; }
        .mobile-btn.down { grid-area: down; }
        .mobile-btn.left { grid-area: left; }
        .mobile-btn.right { grid-area: right; }
        
        @media (max-width: 768px) {
            .mobile-controls {
                display: grid;
            }
            
            .game-canvas {
                max-width: 90vw;
                max-height: 70vh;
            }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <h1 class="game-title">🐪 Camel Caravan</h1>
            <p style="color: #FFD700; font-size: 1.1rem;">Guide your caravan across the desert!</p>
        </div>
        
        <div class="score-panel">
            <div class="score-item">
                <div class="score-label">Score</div>
                <div class="score-value" id="current-score">0</div>
            </div>
            <div class="score-item">
                <div class="score-label">Level</div>
                <div class="score-value" id="level">1</div>
            </div>
            <div class="score-item">
                <div class="score-label">High Score</div>
                <div class="score-value" id="high-score">0</div>
            </div>
        </div>
        
        <div class="lives-display">
            <span class="life-icon" id="life1">🐪</span>
            <span class="life-icon" id="life2">🐪</span>
            <span class="life-icon" id="life3">🐪</span>
        </div>
        
        <canvas id="gameCanvas" class="game-canvas" width="600" height="500"></canvas>
        
        <div class="game-controls">
            <button class="control-btn" id="startBtn">🎮 Start Journey</button>
            <button class="control-btn" id="pauseBtn">⏸️ Pause</button>
            <button class="control-btn" id="resetBtn">🔄 Reset</button>
            <button class="control-btn" id="backBtn" onclick="window.location.href='../index.html'">🏠 Back to Menu</button>
        </div>
        
        <div class="mobile-controls">
            <div class="mobile-btn up">⬆️</div>
            <div class="mobile-btn left">⬅️</div>
            <div class="mobile-btn right">➡️</div>
            <div class="mobile-btn down">⬇️</div>
        </div>
        
        <div class="instructions">
            <h3>🎯 How to Play</h3>
            <ul>
                <li><strong>Desktop:</strong> Use arrow keys to move your camel</li>
                <li><strong>Mobile:</strong> Use the directional buttons</li>
                <li>🐪 Cross desert routes without getting hit by obstacles</li>
                <li>🏺 Avoid sandstorms, rocks, and other hazards</li>
                <li>🌴 Reach oases to advance to the next level</li>
                <li>💎 Collect treasures for bonus points</li>
                <li>❤️ You have 3 lives - use them wisely!</li>
            </ul>
        </div>
    </div>
    
    <script type="module">
        import GameEngine from '../src/utils/GameEngine.js';
        import scoreManager from '../src/utils/ScoreManager.js';
        
        class CamelCaravan extends GameEngine {
            constructor() {
                super('gameCanvas', {
                    width: 600,
                    height: 500,
                    backgroundColor: '#F4E4BC',
                    gameName: 'camel-caravan'
                });
                
                this.gridSize = 50;
                this.rows = 10;
                this.cols = 12;
                
                this.player = {
                    x: 5,
                    y: 9,
                    size: 40
                };
                
                this.obstacles = [];
                this.treasures = [];
                this.lanes = [];
                this.levelComplete = false;
                
                this.setupLanes();
                this.setupUI();
                this.loadHighScore();
                this.updateDisplay();
                this.resetLevel();
            }
            
            setupLanes() {
                // Define different types of lanes
                this.laneTypes = [
                    { type: 'safe', color: '#DEB887', obstacles: [] },
                    { type: 'rocks', color: '#8B4513', speed: 100, direction: 1 },
                    { type: 'sandstorm', color: '#CD853F', speed: 150, direction: -1 },
                    { type: 'quicksand', color: '#D2B48C', speed: 80, direction: 1 },
                    { type: 'oasis', color: '#90EE90', obstacles: [] }
                ];
            }
            
            setupUI() {
                document.getElementById('startBtn').addEventListener('click', () => this.start());
                document.getElementById('pauseBtn').addEventListener('click', () => {
                    if (this.isPaused) this.resume();
                    else this.pause();
                });
                document.getElementById('resetBtn').addEventListener('click', () => this.resetGame());
                
                // Mobile controls
                document.querySelector('.mobile-btn.up').addEventListener('click', () => this.movePlayer(0, -1));
                document.querySelector('.mobile-btn.down').addEventListener('click', () => this.movePlayer(0, 1));
                document.querySelector('.mobile-btn.left').addEventListener('click', () => this.movePlayer(-1, 0));
                document.querySelector('.mobile-btn.right').addEventListener('click', () => this.movePlayer(1, 0));
            }
            
            onKeyDown(event) {
                if (!this.isRunning || this.isPaused) return;
                
                switch(event.code) {
                    case 'ArrowUp':
                    case 'KeyW':
                        this.movePlayer(0, -1);
                        break;
                    case 'ArrowDown':
                    case 'KeyS':
                        this.movePlayer(0, 1);
                        break;
                    case 'ArrowLeft':
                    case 'KeyA':
                        this.movePlayer(-1, 0);
                        break;
                    case 'ArrowRight':
                    case 'KeyD':
                        this.movePlayer(1, 0);
                        break;
                }
            }
            
            movePlayer(dx, dy) {
                const newX = this.player.x + dx;
                const newY = this.player.y + dy;
                
                if (newX >= 0 && newX < this.cols && newY >= 0 && newY < this.rows) {
                    this.player.x = newX;
                    this.player.y = newY;
                    
                    // Check if reached oasis (top row)
                    if (this.player.y === 0) {
                        this.levelComplete = true;
                        this.addScore(100 * this.level);
                        setTimeout(() => this.nextLevel(), 1000);
                    }
                    
                    // Check treasure collection
                    this.checkTreasureCollection();
                }
            }
            
            resetLevel() {
                this.player.x = 5;
                this.player.y = 9;
                this.levelComplete = false;
                this.obstacles = [];
                this.treasures = [];
                this.lanes = [];
                
                // Generate lanes for current level
                for (let row = 0; row < this.rows; row++) {
                    if (row === 0) {
                        // Top row is always oasis (goal)
                        this.lanes[row] = { type: 'oasis', color: '#90EE90', obstacles: [] };
                    } else if (row === this.rows - 1) {
                        // Bottom row is always safe (start)
                        this.lanes[row] = { type: 'safe', color: '#DEB887', obstacles: [] };
                    } else {
                        // Random lane type for middle rows
                        const laneType = this.laneTypes[1 + Math.floor(Math.random() * 3)]; // Skip safe and oasis
                        this.lanes[row] = {
                            type: laneType.type,
                            color: laneType.color,
                            speed: laneType.speed + (this.level - 1) * 20,
                            direction: laneType.direction,
                            obstacles: []
                        };
                        
                        // Generate obstacles for this lane
                        this.generateObstaclesForLane(row);
                    }
                }
                
                // Generate treasures
                this.generateTreasures();
            }
            
            generateObstaclesForLane(row) {
                const lane = this.lanes[row];
                const numObstacles = 2 + Math.floor(Math.random() * 3);
                
                for (let i = 0; i < numObstacles; i++) {
                    const obstacle = {
                        x: (i * this.cols / numObstacles + Math.random() * 2) * this.gridSize,
                        y: row,
                        width: this.gridSize - 10,
                        height: this.gridSize - 10,
                        type: lane.type,
                        speed: lane.speed,
                        direction: lane.direction
                    };
                    
                    lane.obstacles.push(obstacle);
                }
            }
            
            generateTreasures() {
                // Add some treasures in safe spots
                for (let i = 0; i < 3 + this.level; i++) {
                    let x, y;
                    do {
                        x = Math.floor(Math.random() * this.cols);
                        y = 1 + Math.floor(Math.random() * (this.rows - 2));
                    } while (this.isObstacleAt(x, y));
                    
                    this.treasures.push({
                        x: x,
                        y: y,
                        collected: false,
                        value: 25
                    });
                }
            }
            
            isObstacleAt(gridX, gridY) {
                const lane = this.lanes[gridY];
                if (!lane || !lane.obstacles) return false;
                
                return lane.obstacles.some(obstacle => {
                    const obstacleGridX = Math.floor(obstacle.x / this.gridSize);
                    return obstacleGridX === gridX;
                });
            }
            
            checkTreasureCollection() {
                this.treasures.forEach(treasure => {
                    if (!treasure.collected && treasure.x === this.player.x && treasure.y === this.player.y) {
                        treasure.collected = true;
                        this.addScore(treasure.value);
                    }
                });
            }
            
            update(deltaTime) {
                if (!this.isRunning || this.isPaused || this.levelComplete) return;
                
                // Update obstacles
                this.lanes.forEach((lane, row) => {
                    if (lane.obstacles) {
                        lane.obstacles.forEach(obstacle => {
                            obstacle.x += obstacle.speed * obstacle.direction * deltaTime;
                            
                            // Wrap around screen
                            if (obstacle.direction > 0 && obstacle.x > this.canvas.width) {
                                obstacle.x = -obstacle.width;
                            } else if (obstacle.direction < 0 && obstacle.x < -obstacle.width) {
                                obstacle.x = this.canvas.width;
                            }
                        });
                    }
                });
                
                // Check collisions
                this.checkCollisions();
            }
            
            checkCollisions() {
                const playerPixelX = this.player.x * this.gridSize + 5;
                const playerPixelY = this.player.y * this.gridSize + 5;
                const playerRect = {
                    x: playerPixelX,
                    y: playerPixelY,
                    width: this.player.size,
                    height: this.player.size
                };
                
                const lane = this.lanes[this.player.y];
                if (lane && lane.obstacles) {
                    for (let obstacle of lane.obstacles) {
                        const obstacleRect = {
                            x: obstacle.x,
                            y: obstacle.y * this.gridSize + 5,
                            width: obstacle.width,
                            height: obstacle.height
                        };
                        
                        if (this.isColliding(playerRect, obstacleRect)) {
                            this.loseLife();
                            return;
                        }
                    }
                }
            }
            
            isColliding(rect1, rect2) {
                return rect1.x < rect2.x + rect2.width &&
                       rect1.x + rect1.width > rect2.x &&
                       rect1.y < rect2.y + rect2.height &&
                       rect1.y + rect1.height > rect2.y;
            }
            
            loseLife() {
                this.lives--;
                this.updateLivesDisplay();
                
                if (this.lives <= 0) {
                    this.gameOver();
                } else {
                    // Reset player position
                    this.player.x = 5;
                    this.player.y = 9;
                }
            }
            
            nextLevel() {
                this.level++;
                this.resetLevel();
                this.updateDisplay();
            }
            
            render() {
                this.clear();
                
                // Draw lanes
                for (let row = 0; row < this.rows; row++) {
                    const lane = this.lanes[row];
                    if (lane) {
                        this.ctx.fillStyle = lane.color;
                        this.ctx.fillRect(0, row * this.gridSize, this.canvas.width, this.gridSize);
                        
                        // Draw lane borders
                        this.ctx.strokeStyle = 'rgba(0, 0, 0, 0.2)';
                        this.ctx.lineWidth = 1;
                        this.ctx.strokeRect(0, row * this.gridSize, this.canvas.width, this.gridSize);
                    }
                }
                
                // Draw obstacles
                this.lanes.forEach((lane, row) => {
                    if (lane.obstacles) {
                        lane.obstacles.forEach(obstacle => {
                            let color = '#8B4513';
                            let symbol = '🪨';
                            
                            switch(obstacle.type) {
                                case 'rocks':
                                    color = '#696969';
                                    symbol = '🪨';
                                    break;
                                case 'sandstorm':
                                    color = '#DAA520';
                                    symbol = '🌪️';
                                    break;
                                case 'quicksand':
                                    color = '#D2B48C';
                                    symbol = '🕳️';
                                    break;
                            }
                            
                            this.ctx.fillStyle = color;
                            this.ctx.fillRect(obstacle.x, obstacle.y * this.gridSize + 5, obstacle.width, obstacle.height);
                            
                            // Add symbol
                            this.ctx.font = '30px Arial';
                            this.ctx.textAlign = 'center';
                            this.ctx.fillText(symbol, obstacle.x + obstacle.width/2, obstacle.y * this.gridSize + 35);
                        });
                    }
                });
                
                // Draw treasures
                this.treasures.forEach(treasure => {
                    if (!treasure.collected) {
                        this.ctx.fillStyle = '#FFD700';
                        this.ctx.beginPath();
                        this.ctx.arc(
                            treasure.x * this.gridSize + this.gridSize/2,
                            treasure.y * this.gridSize + this.gridSize/2,
                            15, 0, Math.PI * 2
                        );
                        this.ctx.fill();
                        
                        // Treasure symbol
                        this.ctx.font = '20px Arial';
                        this.ctx.textAlign = 'center';
                        this.ctx.fillStyle = '#8B4513';
                        this.ctx.fillText('💎', 
                            treasure.x * this.gridSize + this.gridSize/2,
                            treasure.y * this.gridSize + this.gridSize/2 + 7
                        );
                    }
                });
                
                // Draw player (camel)
                const playerPixelX = this.player.x * this.gridSize + 5;
                const playerPixelY = this.player.y * this.gridSize + 5;
                
                this.ctx.fillStyle = '#D2691E';
                this.ctx.fillRect(playerPixelX, playerPixelY, this.player.size, this.player.size);
                
                // Camel emoji
                this.ctx.font = '35px Arial';
                this.ctx.textAlign = 'center';
                this.ctx.fillText('🐪', playerPixelX + this.player.size/2, playerPixelY + 30);
                
                // Draw goal indicator
                this.ctx.fillStyle = 'rgba(144, 238, 144, 0.5)';
                this.ctx.fillRect(0, 0, this.canvas.width, this.gridSize);
                this.ctx.font = '20px Orbitron';
                this.ctx.fillStyle = '#228B22';
                this.ctx.textAlign = 'center';
                this.ctx.fillText('🌴 OASIS - REACH HERE! 🌴', this.canvas.width/2, 30);
                
                // Level complete message
                if (this.levelComplete) {
                    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
                    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
                    
                    this.drawText('Level Complete!', this.canvas.width / 2, this.canvas.height / 2, {
                        font: '36px Orbitron',
                        color: '#FFD700',
                        align: 'center',
                        baseline: 'middle'
                    });
                }
                
                // Game over screen
                if (!this.isRunning && !this.levelComplete) {
                    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
                    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
                    
                    this.drawText('Game Over!', this.canvas.width / 2, this.canvas.height / 2 - 40, {
                        font: '36px Orbitron',
                        color: '#FFD700',
                        align: 'center',
                        baseline: 'middle'
                    });
                    
                    this.drawText(`Level Reached: ${this.level}`, this.canvas.width / 2, this.canvas.height / 2, {
                        font: '24px Orbitron',
                        color: '#FFF',
                        align: 'center',
                        baseline: 'middle'
                    });
                    
                    this.drawText(`Final Score: ${this.score}`, this.canvas.width / 2, this.canvas.height / 2 + 40, {
                        font: '24px Orbitron',
                        color: '#FFF',
                        align: 'center',
                        baseline: 'middle'
                    });
                }
            }
            
            gameOver() {
                this.stop();
                
                const gameData = {
                    score: this.score,
                    level: this.level,
                    playTime: Date.now() - this.startTime
                };
                
                scoreManager.updatePlayerStats('camel-caravan', gameData);
                
                if (scoreManager.setHighScore('camel-caravan', this.score)) {
                    setTimeout(() => {
                        alert('🎉 New High Score! 🎉');
                    }, 100);
                }
            }
            
            resetGame() {
                this.stop();
                this.resetScore();
                this.level = 1;
                this.lives = 3;
                this.resetLevel();
                this.updateDisplay();
                this.updateLivesDisplay();
            }
            
            updateLivesDisplay() {
                for (let i = 1; i <= 3; i++) {
                    const lifeIcon = document.getElementById(`life${i}`);
                    if (i <= this.lives) {
                        lifeIcon.classList.remove('lost');
                    } else {
                        lifeIcon.classList.add('lost');
                    }
                }
            }
            
            onStart() {
                this.startTime = Date.now();
                document.getElementById('startBtn').textContent = '🐪 Traveling...';
            }
            
            onStop() {
                document.getElementById('startBtn').textContent = '🎮 Start Journey';
            }
            
            updateDisplay() {
                document.getElementById('current-score').textContent = this.score;
                document.getElementById('high-score').textContent = this.highScore;
                document.getElementById('level').textContent = this.level;
            }
        }
        
        const game = new CamelCaravan();
    </script>
</body>
</html>
