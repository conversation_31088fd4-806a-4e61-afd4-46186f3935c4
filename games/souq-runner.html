<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏃‍♂️ Souq Runner - Teepana Games World</title>
    <meta name="description" content="Race through bustling Marrakech markets, dodging vendors and collecting treasures in this endless runner adventure.">
    
    <link rel="stylesheet" href="../src/styles/main.css">
    <style>
        .game-container {
            background: linear-gradient(135deg, #F97316 0%, #EA580C 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }
        
        .game-canvas {
            border: 4px solid #FFD700;
            border-radius: 10px;
            box-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
            background: linear-gradient(to bottom, #87CEEB 0%, #DEB887 70%, #8B4513 100%);
        }
        
        .power-ups {
            display: flex;
            gap: 15px;
            margin: 15px 0;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .power-up {
            background: rgba(0, 0, 0, 0.7);
            padding: 10px 15px;
            border-radius: 8px;
            border: 2px solid #FFD700;
            color: #FFD700;
            font-size: 0.9rem;
            text-align: center;
            min-width: 100px;
        }
        
        .power-up.active {
            background: rgba(255, 215, 0, 0.3);
            animation: pulse 1s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        .mobile-controls {
            display: none;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin: 20px 0;
            max-width: 250px;
        }
        
        .mobile-btn {
            padding: 15px;
            background: rgba(255, 215, 0, 0.2);
            border: 2px solid #FFD700;
            border-radius: 8px;
            color: #FFD700;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .mobile-btn:hover, .mobile-btn:active {
            background: rgba(255, 215, 0, 0.4);
            transform: scale(0.95);
        }
        
        @media (max-width: 768px) {
            .mobile-controls {
                display: grid;
            }
            
            .game-canvas {
                max-width: 90vw;
                max-height: 60vh;
            }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <h1 class="game-title">🏃‍♂️ Souq Runner</h1>
            <p style="color: #FFD700; font-size: 1.1rem;">Race through bustling Marrakech markets!</p>
        </div>
        
        <div class="score-panel">
            <div class="score-item">
                <div class="score-label">Distance</div>
                <div class="score-value" id="distance">0m</div>
            </div>
            <div class="score-item">
                <div class="score-label">Score</div>
                <div class="score-value" id="current-score">0</div>
            </div>
            <div class="score-item">
                <div class="score-label">Coins</div>
                <div class="score-value" id="coins">0</div>
            </div>
            <div class="score-item">
                <div class="score-label">High Score</div>
                <div class="score-value" id="high-score">0</div>
            </div>
        </div>
        
        <div class="power-ups">
            <div class="power-up" id="speed-boost">
                <div>⚡ Speed Boost</div>
                <div id="speed-timer">0s</div>
            </div>
            <div class="power-up" id="coin-magnet">
                <div>🧲 Coin Magnet</div>
                <div id="magnet-timer">0s</div>
            </div>
            <div class="power-up" id="shield">
                <div>🛡️ Shield</div>
                <div id="shield-timer">0s</div>
            </div>
        </div>
        
        <canvas id="gameCanvas" class="game-canvas" width="800" height="400"></canvas>
        
        <div class="game-controls">
            <button class="control-btn" id="startBtn">🎮 Start Running</button>
            <button class="control-btn" id="pauseBtn">⏸️ Pause</button>
            <button class="control-btn" id="resetBtn">🔄 Reset</button>
            <button class="control-btn" id="backBtn" onclick="window.location.href='../index.html'">🏠 Back to Menu</button>
        </div>
        
        <div class="mobile-controls">
            <div class="mobile-btn" id="jumpBtn">⬆️ Jump</div>
            <div class="mobile-btn" id="slideBtn">⬇️ Slide</div>
            <div class="mobile-btn" id="pauseMobileBtn">⏸️ Pause</div>
        </div>
        
        <div class="instructions">
            <h3>🎯 How to Play</h3>
            <ul>
                <li><strong>Desktop:</strong> Space/Up Arrow to jump, Down Arrow to slide</li>
                <li><strong>Mobile:</strong> Use the Jump and Slide buttons</li>
                <li>🪙 Collect golden coins for points</li>
                <li>💎 Grab special treasures for bonus scores</li>
                <li>⚡ Power-ups give temporary abilities</li>
                <li>🏺 Avoid market stalls and obstacles</li>
                <li>🏃‍♂️ The longer you run, the faster it gets!</li>
            </ul>
        </div>
    </div>
    
    <script type="module">
        import GameEngine from '../src/utils/GameEngine.js';
        import scoreManager from '../src/utils/ScoreManager.js';
        
        class SouqRunner extends GameEngine {
            constructor() {
                super('gameCanvas', {
                    width: 800,
                    height: 400,
                    backgroundColor: '#87CEEB',
                    gameName: 'souq-runner'
                });
                
                this.player = {
                    x: 100,
                    y: 300,
                    width: 40,
                    height: 60,
                    velocityY: 0,
                    isJumping: false,
                    isSliding: false,
                    slideTimer: 0
                };
                
                this.ground = 300;
                this.gravity = 1200;
                this.jumpPower = -500;
                this.gameSpeed = 300;
                this.distance = 0;
                this.coins = 0;
                
                this.obstacles = [];
                this.collectibles = [];
                this.powerUps = [];
                this.backgroundElements = [];
                
                // Power-up states
                this.speedBoost = { active: false, timer: 0 };
                this.coinMagnet = { active: false, timer: 0 };
                this.shield = { active: false, timer: 0 };
                
                this.spawnTimer = 0;
                this.spawnInterval = 2;
                
                this.setupUI();
                this.loadHighScore();
                this.updateDisplay();
                this.generateBackground();
            }
            
            setupUI() {
                document.getElementById('startBtn').addEventListener('click', () => this.start());
                document.getElementById('pauseBtn').addEventListener('click', () => {
                    if (this.isPaused) this.resume();
                    else this.pause();
                });
                document.getElementById('resetBtn').addEventListener('click', () => this.resetGame());
                
                // Mobile controls
                document.getElementById('jumpBtn').addEventListener('click', () => this.jump());
                document.getElementById('slideBtn').addEventListener('click', () => this.slide());
                document.getElementById('pauseMobileBtn').addEventListener('click', () => {
                    if (this.isPaused) this.resume();
                    else this.pause();
                });
            }
            
            onKeyDown(event) {
                switch(event.code) {
                    case 'Space':
                    case 'ArrowUp':
                    case 'KeyW':
                        event.preventDefault();
                        this.jump();
                        break;
                    case 'ArrowDown':
                    case 'KeyS':
                        this.slide();
                        break;
                }
            }
            
            jump() {
                if (!this.player.isJumping && !this.player.isSliding) {
                    this.player.velocityY = this.jumpPower;
                    this.player.isJumping = true;
                }
            }
            
            slide() {
                if (!this.player.isJumping) {
                    this.player.isSliding = true;
                    this.player.slideTimer = 0.5; // Slide for 0.5 seconds
                }
            }
            
            generateBackground() {
                // Generate market stalls and buildings in background
                for (let i = 0; i < 10; i++) {
                    this.backgroundElements.push({
                        x: i * 150,
                        type: Math.random() > 0.5 ? 'stall' : 'building',
                        color: ['#D2691E', '#8B4513', '#CD853F'][Math.floor(Math.random() * 3)]
                    });
                }
            }
            
            spawnObstacle() {
                const types = ['stall', 'cart', 'vendor'];
                const type = types[Math.floor(Math.random() * types.length)];
                
                this.obstacles.push({
                    x: this.canvas.width,
                    y: type === 'bird' ? 200 : this.ground,
                    width: type === 'vendor' ? 30 : 50,
                    height: type === 'bird' ? 20 : 60,
                    type: type,
                    color: '#8B4513'
                });
            }
            
            spawnCollectible() {
                const types = ['coin', 'treasure', 'powerup'];
                const type = types[Math.floor(Math.random() * types.length)];
                
                let collectible = {
                    x: this.canvas.width,
                    y: type === 'coin' ? this.ground - 30 - Math.random() * 100 : this.ground - 20,
                    width: 20,
                    height: 20,
                    type: type,
                    value: type === 'coin' ? 10 : type === 'treasure' ? 50 : 0
                };
                
                if (type === 'powerup') {
                    collectible.powerType = ['speed', 'magnet', 'shield'][Math.floor(Math.random() * 3)];
                }
                
                this.collectibles.push(collectible);
            }
            
            update(deltaTime) {
                if (!this.isRunning || this.isPaused) return;
                
                // Update distance and speed
                this.distance += this.gameSpeed * deltaTime / 100;
                this.gameSpeed = Math.min(600, 300 + this.distance / 10);
                
                // Update player physics
                this.updatePlayer(deltaTime);
                
                // Update power-ups
                this.updatePowerUps(deltaTime);
                
                // Spawn objects
                this.spawnTimer += deltaTime;
                if (this.spawnTimer >= this.spawnInterval) {
                    if (Math.random() > 0.3) this.spawnObstacle();
                    if (Math.random() > 0.5) this.spawnCollectible();
                    this.spawnTimer = 0;
                    this.spawnInterval = Math.max(0.8, 2 - this.distance / 1000);
                }
                
                // Update objects
                this.updateObjects(deltaTime);
                
                // Check collisions
                this.checkCollisions();
                
                // Update score
                this.addScore(Math.floor(deltaTime * 10));
                this.updateDisplay();
            }
            
            updatePlayer(deltaTime) {
                // Handle sliding
                if (this.player.isSliding) {
                    this.player.slideTimer -= deltaTime;
                    if (this.player.slideTimer <= 0) {
                        this.player.isSliding = false;
                        this.player.height = 60;
                    } else {
                        this.player.height = 30;
                        this.player.y = this.ground + 30;
                    }
                }
                
                // Handle jumping
                if (this.player.isJumping || this.player.velocityY !== 0) {
                    this.player.velocityY += this.gravity * deltaTime;
                    this.player.y += this.player.velocityY * deltaTime;
                    
                    if (this.player.y >= this.ground) {
                        this.player.y = this.ground;
                        this.player.velocityY = 0;
                        this.player.isJumping = false;
                        if (!this.player.isSliding) {
                            this.player.height = 60;
                        }
                    }
                }
            }
            
            updatePowerUps(deltaTime) {
                // Speed boost
                if (this.speedBoost.active) {
                    this.speedBoost.timer -= deltaTime;
                    if (this.speedBoost.timer <= 0) {
                        this.speedBoost.active = false;
                        document.getElementById('speed-boost').classList.remove('active');
                    }
                }
                
                // Coin magnet
                if (this.coinMagnet.active) {
                    this.coinMagnet.timer -= deltaTime;
                    if (this.coinMagnet.timer <= 0) {
                        this.coinMagnet.active = false;
                        document.getElementById('coin-magnet').classList.remove('active');
                    }
                }
                
                // Shield
                if (this.shield.active) {
                    this.shield.timer -= deltaTime;
                    if (this.shield.timer <= 0) {
                        this.shield.active = false;
                        document.getElementById('shield').classList.remove('active');
                    }
                }
                
                // Update UI
                document.getElementById('speed-timer').textContent = 
                    this.speedBoost.active ? Math.ceil(this.speedBoost.timer) + 's' : '0s';
                document.getElementById('magnet-timer').textContent = 
                    this.coinMagnet.active ? Math.ceil(this.coinMagnet.timer) + 's' : '0s';
                document.getElementById('shield-timer').textContent = 
                    this.shield.active ? Math.ceil(this.shield.timer) + 's' : '0s';
            }
            
            updateObjects(deltaTime) {
                const speed = this.speedBoost.active ? this.gameSpeed * 1.5 : this.gameSpeed;
                
                // Update obstacles
                this.obstacles = this.obstacles.filter(obstacle => {
                    obstacle.x -= speed * deltaTime;
                    return obstacle.x > -obstacle.width;
                });
                
                // Update collectibles
                this.collectibles = this.collectibles.filter(collectible => {
                    collectible.x -= speed * deltaTime;
                    
                    // Coin magnet effect
                    if (this.coinMagnet.active && collectible.type === 'coin') {
                        const dx = this.player.x - collectible.x;
                        const dy = this.player.y - collectible.y;
                        const distance = Math.sqrt(dx * dx + dy * dy);
                        
                        if (distance < 100) {
                            collectible.x += dx * deltaTime * 3;
                            collectible.y += dy * deltaTime * 3;
                        }
                    }
                    
                    return collectible.x > -collectible.width;
                });
                
                // Update background
                this.backgroundElements.forEach(element => {
                    element.x -= speed * deltaTime * 0.3; // Slower parallax
                    if (element.x < -200) {
                        element.x += 1500;
                    }
                });
            }
            
            checkCollisions() {
                const playerRect = {
                    x: this.player.x,
                    y: this.player.y,
                    width: this.player.width,
                    height: this.player.height
                };
                
                // Check obstacle collisions
                for (let obstacle of this.obstacles) {
                    if (this.isColliding(playerRect, obstacle)) {
                        if (!this.shield.active) {
                            this.gameOver();
                            return;
                        } else {
                            // Remove obstacle when shield is active
                            this.obstacles = this.obstacles.filter(o => o !== obstacle);
                        }
                    }
                }
                
                // Check collectible collisions
                this.collectibles = this.collectibles.filter(collectible => {
                    if (this.isColliding(playerRect, collectible)) {
                        this.collectItem(collectible);
                        return false;
                    }
                    return true;
                });
            }
            
            isColliding(rect1, rect2) {
                return rect1.x < rect2.x + rect2.width &&
                       rect1.x + rect1.width > rect2.x &&
                       rect1.y < rect2.y + rect2.height &&
                       rect1.y + rect1.height > rect2.y;
            }
            
            collectItem(item) {
                if (item.type === 'coin') {
                    this.coins++;
                    this.addScore(item.value);
                } else if (item.type === 'treasure') {
                    this.addScore(item.value);
                } else if (item.type === 'powerup') {
                    this.activatePowerUp(item.powerType);
                }
            }
            
            activatePowerUp(type) {
                switch(type) {
                    case 'speed':
                        this.speedBoost.active = true;
                        this.speedBoost.timer = 5;
                        document.getElementById('speed-boost').classList.add('active');
                        break;
                    case 'magnet':
                        this.coinMagnet.active = true;
                        this.coinMagnet.timer = 8;
                        document.getElementById('coin-magnet').classList.add('active');
                        break;
                    case 'shield':
                        this.shield.active = true;
                        this.shield.timer = 6;
                        document.getElementById('shield').classList.add('active');
                        break;
                }
            }
            
            render() {
                this.clear();
                
                // Draw sky gradient
                const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
                gradient.addColorStop(0, '#87CEEB');
                gradient.addColorStop(0.7, '#DEB887');
                gradient.addColorStop(1, '#8B4513');
                this.ctx.fillStyle = gradient;
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
                
                // Draw background elements
                this.backgroundElements.forEach(element => {
                    this.ctx.fillStyle = element.color;
                    if (element.type === 'stall') {
                        this.ctx.fillRect(element.x, 250, 80, 100);
                        // Awning
                        this.ctx.fillStyle = '#DC143C';
                        this.ctx.fillRect(element.x - 10, 240, 100, 20);
                    } else {
                        this.ctx.fillRect(element.x, 200, 60, 150);
                    }
                });
                
                // Draw ground
                this.ctx.fillStyle = '#8B4513';
                this.ctx.fillRect(0, this.ground + 60, this.canvas.width, 40);
                
                // Draw obstacles
                this.obstacles.forEach(obstacle => {
                    this.ctx.fillStyle = obstacle.color;
                    this.ctx.fillRect(obstacle.x, obstacle.y, obstacle.width, obstacle.height);
                    
                    // Add details based on type
                    if (obstacle.type === 'stall') {
                        this.ctx.fillStyle = '#DC143C';
                        this.ctx.fillRect(obstacle.x - 5, obstacle.y - 10, obstacle.width + 10, 10);
                    }
                });
                
                // Draw collectibles
                this.collectibles.forEach(collectible => {
                    if (collectible.type === 'coin') {
                        this.ctx.fillStyle = '#FFD700';
                        this.ctx.beginPath();
                        this.ctx.arc(collectible.x + 10, collectible.y + 10, 10, 0, Math.PI * 2);
                        this.ctx.fill();
                    } else if (collectible.type === 'treasure') {
                        this.ctx.fillStyle = '#FF1493';
                        this.ctx.fillRect(collectible.x, collectible.y, collectible.width, collectible.height);
                    } else if (collectible.type === 'powerup') {
                        this.ctx.fillStyle = '#00FFFF';
                        this.ctx.fillRect(collectible.x, collectible.y, collectible.width, collectible.height);
                        
                        // Glow effect
                        this.ctx.shadowColor = '#00FFFF';
                        this.ctx.shadowBlur = 10;
                        this.ctx.fillRect(collectible.x + 5, collectible.y + 5, 10, 10);
                        this.ctx.shadowBlur = 0;
                    }
                });
                
                // Draw player
                this.ctx.fillStyle = this.shield.active ? '#00FFFF' : '#228B22';
                this.ctx.fillRect(this.player.x, this.player.y, this.player.width, this.player.height);
                
                // Player details
                this.ctx.fillStyle = '#FFD700';
                this.ctx.fillRect(this.player.x + 5, this.player.y + 5, 30, 10); // Turban
                this.ctx.fillStyle = '#FFA500';
                this.ctx.fillRect(this.player.x + 10, this.player.y + 20, 20, 30); // Robe
                
                // Shield effect
                if (this.shield.active) {
                    this.ctx.strokeStyle = '#00FFFF';
                    this.ctx.lineWidth = 3;
                    this.ctx.beginPath();
                    this.ctx.arc(this.player.x + 20, this.player.y + 30, 40, 0, Math.PI * 2);
                    this.ctx.stroke();
                }
                
                // Draw game over screen
                if (!this.isRunning && this.distance > 0) {
                    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
                    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
                    
                    this.drawText('Game Over!', this.canvas.width / 2, this.canvas.height / 2 - 60, {
                        font: '36px Orbitron',
                        color: '#FFD700',
                        align: 'center',
                        baseline: 'middle'
                    });
                    
                    this.drawText(`Distance: ${Math.floor(this.distance)}m`, this.canvas.width / 2, this.canvas.height / 2 - 20, {
                        font: '24px Orbitron',
                        color: '#FFF',
                        align: 'center',
                        baseline: 'middle'
                    });
                    
                    this.drawText(`Score: ${this.score}`, this.canvas.width / 2, this.canvas.height / 2 + 20, {
                        font: '24px Orbitron',
                        color: '#FFF',
                        align: 'center',
                        baseline: 'middle'
                    });
                    
                    this.drawText('Press Start to run again', this.canvas.width / 2, this.canvas.height / 2 + 60, {
                        font: '18px Orbitron',
                        color: '#F97316',
                        align: 'center',
                        baseline: 'middle'
                    });
                }
            }
            
            gameOver() {
                this.stop();
                
                const gameData = {
                    score: this.score,
                    level: Math.floor(this.distance / 100) + 1,
                    playTime: Date.now() - this.startTime
                };
                
                scoreManager.updatePlayerStats('souq-runner', gameData);
                
                if (scoreManager.setHighScore('souq-runner', this.score)) {
                    setTimeout(() => {
                        alert('🎉 New High Score! 🎉');
                    }, 100);
                }
            }
            
            resetGame() {
                this.stop();
                this.player.y = this.ground;
                this.player.velocityY = 0;
                this.player.isJumping = false;
                this.player.isSliding = false;
                this.player.height = 60;
                this.distance = 0;
                this.coins = 0;
                this.gameSpeed = 300;
                this.obstacles = [];
                this.collectibles = [];
                this.spawnTimer = 0;
                
                // Reset power-ups
                this.speedBoost = { active: false, timer: 0 };
                this.coinMagnet = { active: false, timer: 0 };
                this.shield = { active: false, timer: 0 };
                
                document.querySelectorAll('.power-up').forEach(el => el.classList.remove('active'));
                
                this.resetScore();
                this.updateDisplay();
            }
            
            onStart() {
                this.startTime = Date.now();
                document.getElementById('startBtn').textContent = '🏃‍♂️ Running...';
            }
            
            onStop() {
                document.getElementById('startBtn').textContent = '🎮 Start Running';
            }
            
            updateDisplay() {
                document.getElementById('distance').textContent = Math.floor(this.distance) + 'm';
                document.getElementById('current-score').textContent = this.score;
                document.getElementById('coins').textContent = this.coins;
                document.getElementById('high-score').textContent = this.highScore;
            }
        }
        
        const game = new SouqRunner();
    </script>
</body>
</html>
