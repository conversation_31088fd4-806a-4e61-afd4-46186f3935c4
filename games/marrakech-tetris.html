<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧩 Marrakech Tetris - Teepana Games World</title>
    <meta name="description" content="Stack beautiful Moroccan tiles in this colorful twist on the classic puzzle game.">
    
    <link rel="stylesheet" href="../src/styles/main.css">
    <style>
        .game-container {
            background: linear-gradient(135deg, #1E3A8A 0%, #3B82F6 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }
        
        .game-layout {
            display: grid;
            grid-template-columns: 1fr auto 1fr;
            gap: 20px;
            align-items: start;
            max-width: 1000px;
        }
        
        .side-panel {
            background: rgba(0, 0, 0, 0.7);
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #FFD700;
            min-width: 200px;
        }
        
        .game-canvas {
            border: 4px solid #FFD700;
            border-radius: 10px;
            box-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
            background: #000;
        }
        
        .next-piece-canvas {
            border: 2px solid #D2691E;
            border-radius: 8px;
            background: #000;
            margin: 10px 0;
        }
        
        .control-section {
            margin: 15px 0;
        }
        
        .control-section h4 {
            color: #FFD700;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }
        
        .control-list {
            color: #FFF;
            font-size: 0.9rem;
            line-height: 1.4;
        }
        
        .mobile-controls {
            display: none;
            grid-template-areas: 
                ". up ."
                "left down right"
                "rotate drop pause";
            gap: 10px;
            margin: 20px 0;
            max-width: 200px;
        }
        
        .mobile-btn {
            padding: 12px;
            background: rgba(255, 215, 0, 0.2);
            border: 2px solid #FFD700;
            border-radius: 8px;
            color: #FFD700;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .mobile-btn:hover, .mobile-btn:active {
            background: rgba(255, 215, 0, 0.4);
            transform: scale(0.95);
        }
        
        .mobile-btn.up { grid-area: up; }
        .mobile-btn.down { grid-area: down; }
        .mobile-btn.left { grid-area: left; }
        .mobile-btn.right { grid-area: right; }
        .mobile-btn.rotate { grid-area: rotate; }
        .mobile-btn.drop { grid-area: drop; }
        .mobile-btn.pause { grid-area: pause; }
        
        @media (max-width: 768px) {
            .game-layout {
                grid-template-columns: 1fr;
                grid-template-rows: auto auto auto;
            }
            
            .mobile-controls {
                display: grid;
            }
            
            .side-panel {
                min-width: auto;
            }
            
            .game-canvas {
                max-width: 90vw;
            }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <h1 class="game-title">🧩 Marrakech Tetris</h1>
            <p style="color: #FFD700; font-size: 1.1rem;">Stack beautiful Moroccan tiles!</p>
        </div>
        
        <div class="score-panel">
            <div class="score-item">
                <div class="score-label">Score</div>
                <div class="score-value" id="current-score">0</div>
            </div>
            <div class="score-item">
                <div class="score-label">Lines</div>
                <div class="score-value" id="lines-cleared">0</div>
            </div>
            <div class="score-item">
                <div class="score-label">Level</div>
                <div class="score-value" id="level">1</div>
            </div>
            <div class="score-item">
                <div class="score-label">High Score</div>
                <div class="score-value" id="high-score">0</div>
            </div>
        </div>
        
        <div class="game-layout">
            <div class="side-panel">
                <h4>🎯 Next Piece</h4>
                <canvas id="nextCanvas" class="next-piece-canvas" width="120" height="120"></canvas>
                
                <div class="control-section">
                    <h4>🎮 Controls</h4>
                    <div class="control-list">
                        <div>← → Move</div>
                        <div>↓ Soft Drop</div>
                        <div>↑ Rotate</div>
                        <div>Space Hard Drop</div>
                        <div>P Pause</div>
                    </div>
                </div>
            </div>
            
            <div>
                <canvas id="gameCanvas" class="game-canvas" width="300" height="600"></canvas>
            </div>
            
            <div class="side-panel">
                <h4>🏺 Moroccan Patterns</h4>
                <div style="color: #FFF; font-size: 0.9rem; line-height: 1.4;">
                    <div style="color: #DC143C;">■ Terracotta</div>
                    <div style="color: #FFD700;">■ Gold</div>
                    <div style="color: #1E3A8A;">■ Deep Blue</div>
                    <div style="color: #10B981;">■ Mint</div>
                    <div style="color: #7C3AED;">■ Purple</div>
                    <div style="color: #F97316;">■ Orange</div>
                    <div style="color: #EF4444;">■ Red</div>
                </div>
                
                <div class="control-section">
                    <h4>🏆 Achievements</h4>
                    <div style="color: #FFF; font-size: 0.8rem;">
                        <div>🌟 Clear 10 lines</div>
                        <div>⚡ Reach level 5</div>
                        <div>💎 Score 5000 points</div>
                        <div>🔥 Clear 4 lines at once</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="game-controls">
            <button class="control-btn" id="startBtn">🎮 Start Game</button>
            <button class="control-btn" id="pauseBtn">⏸️ Pause</button>
            <button class="control-btn" id="resetBtn">🔄 Reset</button>
            <button class="control-btn" id="backBtn" onclick="window.location.href='../index.html'">🏠 Back to Menu</button>
        </div>
        
        <div class="mobile-controls">
            <div class="mobile-btn up">🔄</div>
            <div class="mobile-btn left">⬅️</div>
            <div class="mobile-btn down">⬇️</div>
            <div class="mobile-btn right">➡️</div>
            <div class="mobile-btn rotate">↻</div>
            <div class="mobile-btn drop">⬇⬇</div>
            <div class="mobile-btn pause">⏸️</div>
        </div>
    </div>
    
    <script type="module">
        import GameEngine from '../src/utils/GameEngine.js';
        import scoreManager from '../src/utils/ScoreManager.js';
        
        class MarrakechTetris extends GameEngine {
            constructor() {
                super('gameCanvas', {
                    width: 300,
                    height: 600,
                    backgroundColor: '#000000',
                    gameName: 'marrakech-tetris'
                });
                
                this.nextCanvas = document.getElementById('nextCanvas');
                this.nextCtx = this.nextCanvas.getContext('2d');
                
                this.blockSize = 30;
                this.boardWidth = 10;
                this.boardHeight = 20;
                this.board = [];
                this.currentPiece = null;
                this.nextPiece = null;
                this.dropTimer = 0;
                this.dropInterval = 1000; // 1 second
                this.linesCleared = 0;
                
                // Moroccan-themed colors
                this.colors = [
                    '#DC143C', // Terracotta Red
                    '#FFD700', // Gold
                    '#1E3A8A', // Deep Blue
                    '#10B981', // Mint
                    '#7C3AED', // Purple
                    '#F97316', // Orange
                    '#EF4444'  // Red
                ];
                
                // Tetris pieces (tetrominoes)
                this.pieces = [
                    // I piece
                    [
                        [1, 1, 1, 1]
                    ],
                    // O piece
                    [
                        [1, 1],
                        [1, 1]
                    ],
                    // T piece
                    [
                        [0, 1, 0],
                        [1, 1, 1]
                    ],
                    // S piece
                    [
                        [0, 1, 1],
                        [1, 1, 0]
                    ],
                    // Z piece
                    [
                        [1, 1, 0],
                        [0, 1, 1]
                    ],
                    // J piece
                    [
                        [1, 0, 0],
                        [1, 1, 1]
                    ],
                    // L piece
                    [
                        [0, 0, 1],
                        [1, 1, 1]
                    ]
                ];
                
                this.initBoard();
                this.setupUI();
                this.loadHighScore();
                this.updateDisplay();
                this.spawnPiece();
            }
            
            initBoard() {
                this.board = Array(this.boardHeight).fill().map(() => Array(this.boardWidth).fill(0));
            }
            
            setupUI() {
                document.getElementById('startBtn').addEventListener('click', () => this.start());
                document.getElementById('pauseBtn').addEventListener('click', () => {
                    if (this.isPaused) this.resume();
                    else this.pause();
                });
                document.getElementById('resetBtn').addEventListener('click', () => this.resetGame());
                
                // Mobile controls
                document.querySelector('.mobile-btn.left').addEventListener('click', () => this.movePiece(-1, 0));
                document.querySelector('.mobile-btn.right').addEventListener('click', () => this.movePiece(1, 0));
                document.querySelector('.mobile-btn.down').addEventListener('click', () => this.movePiece(0, 1));
                document.querySelector('.mobile-btn.up').addEventListener('click', () => this.rotatePiece());
                document.querySelector('.mobile-btn.rotate').addEventListener('click', () => this.rotatePiece());
                document.querySelector('.mobile-btn.drop').addEventListener('click', () => this.hardDrop());
                document.querySelector('.mobile-btn.pause').addEventListener('click', () => {
                    if (this.isPaused) this.resume();
                    else this.pause();
                });
            }
            
            onKeyDown(event) {
                if (!this.isRunning || this.isPaused) return;
                
                switch(event.code) {
                    case 'ArrowLeft':
                    case 'KeyA':
                        this.movePiece(-1, 0);
                        break;
                    case 'ArrowRight':
                    case 'KeyD':
                        this.movePiece(1, 0);
                        break;
                    case 'ArrowDown':
                    case 'KeyS':
                        this.movePiece(0, 1);
                        break;
                    case 'ArrowUp':
                    case 'KeyW':
                        this.rotatePiece();
                        break;
                    case 'Space':
                        event.preventDefault();
                        this.hardDrop();
                        break;
                    case 'KeyP':
                        if (this.isPaused) this.resume();
                        else this.pause();
                        break;
                }
            }
            
            spawnPiece() {
                if (!this.nextPiece) {
                    this.nextPiece = this.createRandomPiece();
                }
                
                this.currentPiece = this.nextPiece;
                this.nextPiece = this.createRandomPiece();
                
                // Check game over
                if (!this.isValidPosition(this.currentPiece)) {
                    this.gameOver();
                    return;
                }
                
                this.drawNextPiece();
            }
            
            createRandomPiece() {
                const shapeIndex = Math.floor(Math.random() * this.pieces.length);
                const colorIndex = Math.floor(Math.random() * this.colors.length);
                
                return {
                    shape: this.pieces[shapeIndex],
                    color: colorIndex + 1,
                    x: Math.floor(this.boardWidth / 2) - Math.floor(this.pieces[shapeIndex][0].length / 2),
                    y: 0
                };
            }
            
            isValidPosition(piece, offsetX = 0, offsetY = 0) {
                for (let y = 0; y < piece.shape.length; y++) {
                    for (let x = 0; x < piece.shape[y].length; x++) {
                        if (piece.shape[y][x]) {
                            const newX = piece.x + x + offsetX;
                            const newY = piece.y + y + offsetY;
                            
                            if (newX < 0 || newX >= this.boardWidth || 
                                newY >= this.boardHeight ||
                                (newY >= 0 && this.board[newY][newX])) {
                                return false;
                            }
                        }
                    }
                }
                return true;
            }
            
            movePiece(dx, dy) {
                if (this.isValidPosition(this.currentPiece, dx, dy)) {
                    this.currentPiece.x += dx;
                    this.currentPiece.y += dy;
                    return true;
                }
                return false;
            }
            
            rotatePiece() {
                const rotated = this.rotateMatrix(this.currentPiece.shape);
                const originalShape = this.currentPiece.shape;
                this.currentPiece.shape = rotated;
                
                if (!this.isValidPosition(this.currentPiece)) {
                    this.currentPiece.shape = originalShape;
                }
            }
            
            rotateMatrix(matrix) {
                const rows = matrix.length;
                const cols = matrix[0].length;
                const rotated = Array(cols).fill().map(() => Array(rows).fill(0));
                
                for (let i = 0; i < rows; i++) {
                    for (let j = 0; j < cols; j++) {
                        rotated[j][rows - 1 - i] = matrix[i][j];
                    }
                }
                
                return rotated;
            }
            
            hardDrop() {
                while (this.movePiece(0, 1)) {
                    this.addScore(2);
                }
                this.lockPiece();
            }
            
            lockPiece() {
                // Place piece on board
                for (let y = 0; y < this.currentPiece.shape.length; y++) {
                    for (let x = 0; x < this.currentPiece.shape[y].length; x++) {
                        if (this.currentPiece.shape[y][x]) {
                            const boardY = this.currentPiece.y + y;
                            const boardX = this.currentPiece.x + x;
                            if (boardY >= 0) {
                                this.board[boardY][boardX] = this.currentPiece.color;
                            }
                        }
                    }
                }
                
                // Check for completed lines
                this.clearLines();
                this.spawnPiece();
            }
            
            clearLines() {
                let linesCleared = 0;
                
                for (let y = this.boardHeight - 1; y >= 0; y--) {
                    if (this.board[y].every(cell => cell !== 0)) {
                        this.board.splice(y, 1);
                        this.board.unshift(Array(this.boardWidth).fill(0));
                        linesCleared++;
                        y++; // Check the same line again
                    }
                }
                
                if (linesCleared > 0) {
                    this.linesCleared += linesCleared;
                    
                    // Scoring system
                    const points = [0, 100, 300, 500, 800][linesCleared] * this.level;
                    this.addScore(points);
                    
                    // Level up every 10 lines
                    this.level = Math.floor(this.linesCleared / 10) + 1;
                    this.dropInterval = Math.max(50, 1000 - (this.level - 1) * 50);
                    
                    this.updateDisplay();
                }
            }
            
            update(deltaTime) {
                if (!this.isRunning || this.isPaused || !this.currentPiece) return;
                
                this.dropTimer += deltaTime * 1000;
                
                if (this.dropTimer >= this.dropInterval) {
                    if (!this.movePiece(0, 1)) {
                        this.lockPiece();
                    }
                    this.dropTimer = 0;
                }
            }
            
            render() {
                this.clear();
                
                // Draw board
                for (let y = 0; y < this.boardHeight; y++) {
                    for (let x = 0; x < this.boardWidth; x++) {
                        if (this.board[y][x]) {
                            this.drawBlock(x, y, this.colors[this.board[y][x] - 1]);
                        }
                    }
                }
                
                // Draw current piece
                if (this.currentPiece) {
                    for (let y = 0; y < this.currentPiece.shape.length; y++) {
                        for (let x = 0; x < this.currentPiece.shape[y].length; x++) {
                            if (this.currentPiece.shape[y][x]) {
                                this.drawBlock(
                                    this.currentPiece.x + x,
                                    this.currentPiece.y + y,
                                    this.colors[this.currentPiece.color - 1]
                                );
                            }
                        }
                    }
                }
                
                // Draw grid
                this.drawGrid();
                
                // Draw game over screen
                if (!this.isRunning && this.linesCleared > 0) {
                    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
                    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
                    
                    this.drawText('Game Over!', this.canvas.width / 2, this.canvas.height / 2 - 40, {
                        font: '32px Orbitron',
                        color: '#FFD700',
                        align: 'center',
                        baseline: 'middle'
                    });
                    
                    this.drawText(`Lines: ${this.linesCleared}`, this.canvas.width / 2, this.canvas.height / 2, {
                        font: '20px Orbitron',
                        color: '#FFF',
                        align: 'center',
                        baseline: 'middle'
                    });
                    
                    this.drawText(`Score: ${this.score}`, this.canvas.width / 2, this.canvas.height / 2 + 30, {
                        font: '20px Orbitron',
                        color: '#FFF',
                        align: 'center',
                        baseline: 'middle'
                    });
                }
            }
            
            drawBlock(x, y, color) {
                const pixelX = x * this.blockSize;
                const pixelY = y * this.blockSize;
                
                // Main block
                this.ctx.fillStyle = color;
                this.ctx.fillRect(pixelX, pixelY, this.blockSize, this.blockSize);
                
                // Moroccan pattern overlay
                this.ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
                this.ctx.fillRect(pixelX + 2, pixelY + 2, this.blockSize - 4, this.blockSize - 4);
                
                // Border
                this.ctx.strokeStyle = 'rgba(0, 0, 0, 0.3)';
                this.ctx.lineWidth = 1;
                this.ctx.strokeRect(pixelX, pixelY, this.blockSize, this.blockSize);
            }
            
            drawGrid() {
                this.ctx.strokeStyle = 'rgba(255, 215, 0, 0.1)';
                this.ctx.lineWidth = 1;
                
                for (let x = 0; x <= this.boardWidth; x++) {
                    this.ctx.beginPath();
                    this.ctx.moveTo(x * this.blockSize, 0);
                    this.ctx.lineTo(x * this.blockSize, this.canvas.height);
                    this.ctx.stroke();
                }
                
                for (let y = 0; y <= this.boardHeight; y++) {
                    this.ctx.beginPath();
                    this.ctx.moveTo(0, y * this.blockSize);
                    this.ctx.lineTo(this.canvas.width, y * this.blockSize);
                    this.ctx.stroke();
                }
            }
            
            drawNextPiece() {
                this.nextCtx.fillStyle = '#000';
                this.nextCtx.fillRect(0, 0, this.nextCanvas.width, this.nextCanvas.height);
                
                if (this.nextPiece) {
                    const blockSize = 20;
                    const offsetX = (this.nextCanvas.width - this.nextPiece.shape[0].length * blockSize) / 2;
                    const offsetY = (this.nextCanvas.height - this.nextPiece.shape.length * blockSize) / 2;
                    
                    for (let y = 0; y < this.nextPiece.shape.length; y++) {
                        for (let x = 0; x < this.nextPiece.shape[y].length; x++) {
                            if (this.nextPiece.shape[y][x]) {
                                this.nextCtx.fillStyle = this.colors[this.nextPiece.color - 1];
                                this.nextCtx.fillRect(
                                    offsetX + x * blockSize,
                                    offsetY + y * blockSize,
                                    blockSize,
                                    blockSize
                                );
                                
                                this.nextCtx.strokeStyle = 'rgba(0, 0, 0, 0.3)';
                                this.nextCtx.strokeRect(
                                    offsetX + x * blockSize,
                                    offsetY + y * blockSize,
                                    blockSize,
                                    blockSize
                                );
                            }
                        }
                    }
                }
            }
            
            gameOver() {
                this.stop();
                
                const gameData = {
                    score: this.score,
                    level: this.level,
                    playTime: Date.now() - this.startTime
                };
                
                scoreManager.updatePlayerStats('marrakech-tetris', gameData);
                
                if (scoreManager.setHighScore('marrakech-tetris', this.score)) {
                    setTimeout(() => {
                        alert('🎉 New High Score! 🎉');
                    }, 100);
                }
            }
            
            resetGame() {
                this.stop();
                this.initBoard();
                this.resetScore();
                this.linesCleared = 0;
                this.level = 1;
                this.dropInterval = 1000;
                this.dropTimer = 0;
                this.currentPiece = null;
                this.nextPiece = null;
                this.spawnPiece();
                this.updateDisplay();
            }
            
            onStart() {
                this.startTime = Date.now();
                document.getElementById('startBtn').textContent = '🎮 Running...';
            }
            
            onStop() {
                document.getElementById('startBtn').textContent = '🎮 Start Game';
            }
            
            updateDisplay() {
                document.getElementById('current-score').textContent = this.score;
                document.getElementById('high-score').textContent = this.highScore;
                document.getElementById('level').textContent = this.level;
                document.getElementById('lines-cleared').textContent = this.linesCleared;
            }
        }
        
        const game = new MarrakechTetris();
    </script>
</body>
</html>
