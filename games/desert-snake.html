<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🐍 Desert Snake - Teepana Games World</title>
    <meta name="description" content="Navigate the Sahara sands collecting precious spices while avoiding desert dangers in this Moroccan-themed Snake game.">
    
    <link rel="stylesheet" href="../src/styles/main.css">
    <style>
        .game-container {
            background: linear-gradient(135deg, #D2691E 0%, #A0522D 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }
        
        .game-header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .game-title {
            font-size: 2.5rem;
            color: #FFD700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            margin-bottom: 10px;
        }
        
        .game-canvas {
            border: 4px solid #FFD700;
            border-radius: 10px;
            box-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
            background: #8B4513;
        }
        
        .game-controls {
            display: flex;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .control-btn {
            padding: 10px 20px;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            color: #000;
            border: none;
            border-radius: 8px;
            font-family: 'Orbitron', sans-serif;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
        }
        
        .score-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin: 20px 0;
            max-width: 600px;
        }
        
        .score-item {
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border: 2px solid #FFD700;
        }
        
        .score-label {
            font-size: 0.9rem;
            color: #FFD700;
            margin-bottom: 5px;
        }
        
        .score-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #FFF;
        }
        
        .instructions {
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 10px;
            max-width: 600px;
            margin: 20px 0;
            border: 2px solid #D2691E;
        }
        
        .instructions h3 {
            color: #FFD700;
            margin-bottom: 15px;
        }
        
        .instructions ul {
            color: #FFF;
            line-height: 1.6;
        }
        
        .mobile-controls {
            display: none;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            max-width: 200px;
            margin: 20px 0;
        }
        
        .mobile-btn {
            padding: 15px;
            background: rgba(255, 215, 0, 0.2);
            border: 2px solid #FFD700;
            border-radius: 8px;
            color: #FFD700;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .mobile-btn:hover, .mobile-btn:active {
            background: rgba(255, 215, 0, 0.4);
            transform: scale(0.95);
        }
        
        @media (max-width: 768px) {
            .mobile-controls {
                display: grid;
            }
            
            .game-canvas {
                max-width: 90vw;
                max-height: 60vh;
            }
            
            .game-title {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <h1 class="game-title">🐍 Desert Snake</h1>
            <p style="color: #FFD700; font-size: 1.1rem;">Navigate the Sahara collecting precious spices!</p>
        </div>
        
        <div class="score-panel">
            <div class="score-item">
                <div class="score-label">Score</div>
                <div class="score-value" id="current-score">0</div>
            </div>
            <div class="score-item">
                <div class="score-label">High Score</div>
                <div class="score-value" id="high-score">0</div>
            </div>
            <div class="score-item">
                <div class="score-label">Level</div>
                <div class="score-value" id="level">1</div>
            </div>
            <div class="score-item">
                <div class="score-label">Spices</div>
                <div class="score-value" id="spices">0</div>
            </div>
        </div>
        
        <canvas id="gameCanvas" class="game-canvas" width="600" height="400"></canvas>
        
        <div class="game-controls">
            <button class="control-btn" id="startBtn">🎮 Start Game</button>
            <button class="control-btn" id="pauseBtn">⏸️ Pause</button>
            <button class="control-btn" id="resetBtn">🔄 Reset</button>
            <button class="control-btn" id="backBtn" onclick="window.location.href='../index.html'">🏠 Back to Menu</button>
        </div>
        
        <div class="mobile-controls">
            <div></div>
            <div class="mobile-btn" id="upBtn">⬆️</div>
            <div></div>
            <div class="mobile-btn" id="leftBtn">⬅️</div>
            <div class="mobile-btn" id="downBtn">⬇️</div>
            <div class="mobile-btn" id="rightBtn">➡️</div>
        </div>
        
        <div class="instructions">
            <h3>🎯 How to Play</h3>
            <ul>
                <li><strong>Desktop:</strong> Use arrow keys to control the snake</li>
                <li><strong>Mobile:</strong> Use the directional buttons below</li>
                <li>🌶️ Collect spices to grow and increase your score</li>
                <li>🏺 Special golden spices give bonus points</li>
                <li>🐍 Avoid hitting walls or your own tail</li>
                <li>⚡ Speed increases as you level up</li>
                <li>🏆 Try to beat your high score!</li>
            </ul>
        </div>
    </div>
    
    <script type="module">
        import GameEngine from '../src/utils/GameEngine.js';
        import scoreManager from '../src/utils/ScoreManager.js';
        
        class DesertSnake extends GameEngine {
            constructor() {
                super('gameCanvas', {
                    width: 600,
                    height: 400,
                    backgroundColor: '#8B4513',
                    gameName: 'desert-snake'
                });
                
                this.gridSize = 20;
                this.snake = [{ x: 10, y: 10 }];
                this.direction = { x: 1, y: 0 };
                this.nextDirection = { x: 1, y: 0 };
                this.food = this.generateFood();
                this.specialFood = null;
                this.specialFoodTimer = 0;
                this.gameSpeed = 150;
                this.spicesCollected = 0;
                
                this.setupUI();
                this.loadHighScore();
                this.updateDisplay();
            }
            
            setupUI() {
                document.getElementById('startBtn').addEventListener('click', () => this.start());
                document.getElementById('pauseBtn').addEventListener('click', () => {
                    if (this.isPaused) this.resume();
                    else this.pause();
                });
                document.getElementById('resetBtn').addEventListener('click', () => this.resetGame());
                
                // Mobile controls
                document.getElementById('upBtn').addEventListener('click', () => this.changeDirection(0, -1));
                document.getElementById('downBtn').addEventListener('click', () => this.changeDirection(0, 1));
                document.getElementById('leftBtn').addEventListener('click', () => this.changeDirection(-1, 0));
                document.getElementById('rightBtn').addEventListener('click', () => this.changeDirection(1, 0));
            }
            
            onKeyDown(event) {
                switch(event.code) {
                    case 'ArrowUp':
                    case 'KeyW':
                        this.changeDirection(0, -1);
                        break;
                    case 'ArrowDown':
                    case 'KeyS':
                        this.changeDirection(0, 1);
                        break;
                    case 'ArrowLeft':
                    case 'KeyA':
                        this.changeDirection(-1, 0);
                        break;
                    case 'ArrowRight':
                    case 'KeyD':
                        this.changeDirection(1, 0);
                        break;
                    case 'Space':
                        event.preventDefault();
                        if (this.isRunning) {
                            if (this.isPaused) this.resume();
                            else this.pause();
                        } else {
                            this.start();
                        }
                        break;
                }
            }
            
            changeDirection(x, y) {
                // Prevent reversing into itself
                if (this.direction.x !== -x || this.direction.y !== -y) {
                    this.nextDirection = { x, y };
                }
            }
            
            generateFood() {
                let food;
                do {
                    food = {
                        x: Math.floor(Math.random() * (this.canvas.width / this.gridSize)),
                        y: Math.floor(Math.random() * (this.canvas.height / this.gridSize))
                    };
                } while (this.snake.some(segment => segment.x === food.x && segment.y === food.y));
                return food;
            }
            
            update(deltaTime) {
                if (!this.isRunning || this.isPaused) return;
                
                // Update game speed based on level
                const targetSpeed = Math.max(50, 150 - (this.level - 1) * 10);
                this.gameSpeed = targetSpeed;
                
                // Move snake at controlled speed
                this.moveTimer = (this.moveTimer || 0) + deltaTime * 1000;
                if (this.moveTimer >= this.gameSpeed) {
                    this.moveTimer = 0;
                    this.moveSnake();
                }
                
                // Special food timer
                this.specialFoodTimer += deltaTime;
                if (this.specialFoodTimer > 10 && !this.specialFood) {
                    this.specialFood = this.generateFood();
                    this.specialFoodTimer = 0;
                }
                
                // Remove special food after 5 seconds
                if (this.specialFood && this.specialFoodTimer > 5) {
                    this.specialFood = null;
                    this.specialFoodTimer = 0;
                }
            }
            
            moveSnake() {
                this.direction = { ...this.nextDirection };
                
                const head = { ...this.snake[0] };
                head.x += this.direction.x;
                head.y += this.direction.y;
                
                // Check wall collision
                if (head.x < 0 || head.x >= this.canvas.width / this.gridSize ||
                    head.y < 0 || head.y >= this.canvas.height / this.gridSize) {
                    this.gameOver();
                    return;
                }
                
                // Check self collision
                if (this.snake.some(segment => segment.x === head.x && segment.y === head.y)) {
                    this.gameOver();
                    return;
                }
                
                this.snake.unshift(head);
                
                // Check food collision
                let foodEaten = false;
                let points = 10;
                
                if (head.x === this.food.x && head.y === this.food.y) {
                    foodEaten = true;
                    this.food = this.generateFood();
                    this.spicesCollected++;
                }
                
                // Check special food collision
                if (this.specialFood && head.x === this.specialFood.x && head.y === this.specialFood.y) {
                    foodEaten = true;
                    points = 50;
                    this.specialFood = null;
                    this.specialFoodTimer = 0;
                    this.spicesCollected += 3;
                }
                
                if (foodEaten) {
                    this.addScore(points);
                    this.level = Math.floor(this.spicesCollected / 5) + 1;
                } else {
                    this.snake.pop();
                }
                
                this.updateDisplay();
            }
            
            render() {
                this.clear();
                
                // Draw desert background pattern
                this.ctx.fillStyle = '#DEB887';
                for (let x = 0; x < this.canvas.width; x += 40) {
                    for (let y = 0; y < this.canvas.height; y += 40) {
                        if ((x + y) % 80 === 0) {
                            this.ctx.fillRect(x, y, 20, 20);
                        }
                    }
                }
                
                // Draw snake
                this.snake.forEach((segment, index) => {
                    if (index === 0) {
                        // Snake head
                        this.ctx.fillStyle = '#228B22';
                        this.ctx.fillRect(segment.x * this.gridSize + 2, segment.y * this.gridSize + 2, 
                                        this.gridSize - 4, this.gridSize - 4);
                        
                        // Eyes
                        this.ctx.fillStyle = '#FFD700';
                        this.ctx.fillRect(segment.x * this.gridSize + 6, segment.y * this.gridSize + 6, 3, 3);
                        this.ctx.fillRect(segment.x * this.gridSize + 11, segment.y * this.gridSize + 6, 3, 3);
                    } else {
                        // Snake body
                        this.ctx.fillStyle = index % 2 === 0 ? '#32CD32' : '#228B22';
                        this.ctx.fillRect(segment.x * this.gridSize + 1, segment.y * this.gridSize + 1, 
                                        this.gridSize - 2, this.gridSize - 2);
                    }
                });
                
                // Draw regular food (spice)
                this.ctx.fillStyle = '#DC143C';
                this.ctx.fillRect(this.food.x * this.gridSize + 3, this.food.y * this.gridSize + 3, 
                                this.gridSize - 6, this.gridSize - 6);
                this.ctx.fillStyle = '#FFD700';
                this.ctx.fillRect(this.food.x * this.gridSize + 6, this.food.y * this.gridSize + 6, 
                                this.gridSize - 12, this.gridSize - 12);
                
                // Draw special food (golden spice)
                if (this.specialFood) {
                    this.ctx.fillStyle = '#FFD700';
                    this.ctx.fillRect(this.specialFood.x * this.gridSize + 1, this.specialFood.y * this.gridSize + 1, 
                                    this.gridSize - 2, this.gridSize - 2);
                    
                    // Glowing effect
                    this.ctx.shadowColor = '#FFD700';
                    this.ctx.shadowBlur = 10;
                    this.ctx.fillRect(this.specialFood.x * this.gridSize + 4, this.specialFood.y * this.gridSize + 4, 
                                    this.gridSize - 8, this.gridSize - 8);
                    this.ctx.shadowBlur = 0;
                }
                
                // Draw game over screen
                if (!this.isRunning && this.snake.length > 1) {
                    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
                    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
                    
                    this.drawText('Game Over!', this.canvas.width / 2, this.canvas.height / 2 - 40, {
                        font: '36px Orbitron',
                        color: '#FFD700',
                        align: 'center',
                        baseline: 'middle'
                    });
                    
                    this.drawText(`Final Score: ${this.score}`, this.canvas.width / 2, this.canvas.height / 2, {
                        font: '24px Orbitron',
                        color: '#FFF',
                        align: 'center',
                        baseline: 'middle'
                    });
                    
                    this.drawText('Press Start to play again', this.canvas.width / 2, this.canvas.height / 2 + 40, {
                        font: '18px Orbitron',
                        color: '#D2691E',
                        align: 'center',
                        baseline: 'middle'
                    });
                }
            }
            
            gameOver() {
                this.stop();
                
                // Update statistics
                const gameData = {
                    score: this.score,
                    level: this.level,
                    playTime: Date.now() - this.startTime
                };
                
                scoreManager.updatePlayerStats('desert-snake', gameData);
                
                // Check for new high score
                if (scoreManager.setHighScore('desert-snake', this.score)) {
                    setTimeout(() => {
                        alert('🎉 New High Score! 🎉');
                    }, 100);
                }
            }
            
            resetGame() {
                this.stop();
                this.snake = [{ x: 10, y: 10 }];
                this.direction = { x: 1, y: 0 };
                this.nextDirection = { x: 1, y: 0 };
                this.food = this.generateFood();
                this.specialFood = null;
                this.specialFoodTimer = 0;
                this.resetScore();
                this.spicesCollected = 0;
                this.gameSpeed = 150;
                this.updateDisplay();
            }
            
            onStart() {
                this.startTime = Date.now();
                document.getElementById('startBtn').textContent = '🎮 Running...';
                document.getElementById('pauseBtn').textContent = '⏸️ Pause';
            }
            
            onStop() {
                document.getElementById('startBtn').textContent = '🎮 Start Game';
                document.getElementById('pauseBtn').textContent = '⏸️ Pause';
            }
            
            onPause() {
                document.getElementById('pauseBtn').textContent = '▶️ Resume';
            }
            
            onResume() {
                document.getElementById('pauseBtn').textContent = '⏸️ Pause';
            }
            
            updateDisplay() {
                document.getElementById('current-score').textContent = this.score;
                document.getElementById('high-score').textContent = this.highScore;
                document.getElementById('level').textContent = this.level;
                document.getElementById('spices').textContent = this.spicesCollected;
            }
        }
        
        // Initialize game
        const game = new DesertSnake();
    </script>
</body>
</html>
