<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🛸 Atlas Invaders - Teepana Games World</title>
    <meta name="description" content="Defend Morocco with flying carpets in this space invaders adventure with Moroccan flair.">
    
    <link rel="stylesheet" href="../src/styles/main.css">
    <style>
        .game-container {
            background: linear-gradient(135deg, #1E3A8A 0%, #7C3AED 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }
        
        .game-canvas {
            border: 4px solid #FFD700;
            border-radius: 10px;
            box-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
            background: linear-gradient(to bottom, #000428 0%, #004e92 100%);
        }
        
        .wave-info {
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 10px;
            border: 2px solid #FFD700;
            margin: 15px 0;
            text-align: center;
            color: #FFD700;
        }
        
        .mobile-controls {
            display: none;
            grid-template-areas: 
                ". . ."
                "left . right"
                ". fire .";
            gap: 15px;
            margin: 20px 0;
            max-width: 200px;
        }
        
        .mobile-btn {
            padding: 15px;
            background: rgba(255, 215, 0, 0.2);
            border: 2px solid #FFD700;
            border-radius: 8px;
            color: #FFD700;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .mobile-btn:hover, .mobile-btn:active {
            background: rgba(255, 215, 0, 0.4);
            transform: scale(0.95);
        }
        
        .mobile-btn.left { grid-area: left; }
        .mobile-btn.right { grid-area: right; }
        .mobile-btn.fire { grid-area: fire; }
        
        @media (max-width: 768px) {
            .mobile-controls {
                display: grid;
            }
            
            .game-canvas {
                max-width: 90vw;
                max-height: 60vh;
            }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <h1 class="game-title">🛸 Atlas Invaders</h1>
            <p style="color: #FFD700; font-size: 1.1rem;">Defend Morocco with flying carpets!</p>
        </div>
        
        <div class="score-panel">
            <div class="score-item">
                <div class="score-label">Score</div>
                <div class="score-value" id="current-score">0</div>
            </div>
            <div class="score-item">
                <div class="score-label">Wave</div>
                <div class="score-value" id="wave">1</div>
            </div>
            <div class="score-item">
                <div class="score-label">Lives</div>
                <div class="score-value" id="lives">3</div>
            </div>
            <div class="score-item">
                <div class="score-label">High Score</div>
                <div class="score-value" id="high-score">0</div>
            </div>
        </div>
        
        <div class="wave-info">
            <div id="wave-status">Wave 1 - Desert Scouts</div>
            <div style="font-size: 0.9rem; opacity: 0.8;">Enemies: <span id="enemies-left">24</span></div>
        </div>
        
        <canvas id="gameCanvas" class="game-canvas" width="700" height="500"></canvas>
        
        <div class="game-controls">
            <button class="control-btn" id="startBtn">🎮 Start Defense</button>
            <button class="control-btn" id="pauseBtn">⏸️ Pause</button>
            <button class="control-btn" id="resetBtn">🔄 Reset</button>
            <button class="control-btn" id="backBtn" onclick="window.location.href='../index.html'">🏠 Back to Menu</button>
        </div>
        
        <div class="mobile-controls">
            <div class="mobile-btn left">⬅️</div>
            <div class="mobile-btn right">➡️</div>
            <div class="mobile-btn fire">🔥 Fire</div>
        </div>
        
        <div class="instructions">
            <h3>🎯 How to Play</h3>
            <ul>
                <li><strong>Desktop:</strong> Arrow keys to move, Space to fire</li>
                <li><strong>Mobile:</strong> Use the control buttons</li>
                <li>🧞‍♂️ Shoot down invading genies and flying carpets</li>
                <li>🏺 Destroy barriers for cover but they can be destroyed</li>
                <li>⚡ Power-ups occasionally drop from defeated enemies</li>
                <li>🌟 Complete waves to advance and face stronger enemies</li>
                <li>🛡️ Protect Morocco from the mystical invasion!</li>
            </ul>
        </div>
    </div>
    
    <script type="module">
        import GameEngine from '../src/utils/GameEngine.js';
        import scoreManager from '../src/utils/ScoreManager.js';
        
        class AtlasInvaders extends GameEngine {
            constructor() {
                super('gameCanvas', {
                    width: 700,
                    height: 500,
                    backgroundColor: '#000428',
                    gameName: 'atlas-invaders'
                });
                
                this.player = {
                    x: this.canvas.width / 2 - 25,
                    y: this.canvas.height - 60,
                    width: 50,
                    height: 30,
                    speed: 300
                };
                
                this.bullets = [];
                this.enemyBullets = [];
                this.enemies = [];
                this.barriers = [];
                this.powerUps = [];
                
                this.wave = 1;
                this.enemyDirection = 1;
                this.enemySpeed = 50;
                this.enemyDropDistance = 30;
                this.lastEnemyShot = 0;
                this.enemyShootInterval = 2000;
                
                this.setupUI();
                this.loadHighScore();
                this.updateDisplay();
                this.initWave();
            }
            
            setupUI() {
                document.getElementById('startBtn').addEventListener('click', () => this.start());
                document.getElementById('pauseBtn').addEventListener('click', () => {
                    if (this.isPaused) this.resume();
                    else this.pause();
                });
                document.getElementById('resetBtn').addEventListener('click', () => this.resetGame());
                
                // Mobile controls
                document.querySelector('.mobile-btn.left').addEventListener('click', () => {
                    this.keys['ArrowLeft'] = true;
                    setTimeout(() => this.keys['ArrowLeft'] = false, 100);
                });
                document.querySelector('.mobile-btn.right').addEventListener('click', () => {
                    this.keys['ArrowRight'] = true;
                    setTimeout(() => this.keys['ArrowRight'] = false, 100);
                });
                document.querySelector('.mobile-btn.fire').addEventListener('click', () => this.shoot());
            }
            
            onKeyDown(event) {
                if (!this.isRunning || this.isPaused) return;
                
                switch(event.code) {
                    case 'Space':
                        event.preventDefault();
                        this.shoot();
                        break;
                }
            }
            
            shoot() {
                // Limit firing rate
                if (Date.now() - this.lastPlayerShot < 200) return;
                this.lastPlayerShot = Date.now();
                
                this.bullets.push({
                    x: this.player.x + this.player.width / 2 - 2,
                    y: this.player.y,
                    width: 4,
                    height: 10,
                    speed: 400,
                    color: '#FFD700'
                });
            }
            
            initWave() {
                this.enemies = [];
                this.bullets = [];
                this.enemyBullets = [];
                
                // Create enemy formation
                const rows = 4 + Math.floor(this.wave / 3);
                const cols = 6 + Math.floor(this.wave / 2);
                const enemyWidth = 40;
                const enemyHeight = 30;
                const spacing = 60;
                const startX = (this.canvas.width - (cols * spacing)) / 2;
                const startY = 50;
                
                for (let row = 0; row < rows; row++) {
                    for (let col = 0; col < cols; col++) {
                        let enemyType = 'scout';
                        let points = 10;
                        let color = '#DC143C';
                        
                        if (row === 0) {
                            enemyType = 'commander';
                            points = 50;
                            color = '#FFD700';
                        } else if (row < 2) {
                            enemyType = 'warrior';
                            points = 30;
                            color = '#FF4500';
                        }
                        
                        this.enemies.push({
                            x: startX + col * spacing,
                            y: startY + row * spacing,
                            width: enemyWidth,
                            height: enemyHeight,
                            type: enemyType,
                            points: points,
                            color: color,
                            originalX: startX + col * spacing
                        });
                    }
                }
                
                // Create barriers
                this.createBarriers();
                
                // Update wave info
                this.updateWaveInfo();
            }
            
            createBarriers() {
                this.barriers = [];
                const barrierCount = 4;
                const barrierWidth = 60;
                const barrierHeight = 40;
                const spacing = (this.canvas.width - barrierCount * barrierWidth) / (barrierCount + 1);
                
                for (let i = 0; i < barrierCount; i++) {
                    const barrier = {
                        x: spacing + i * (barrierWidth + spacing),
                        y: this.canvas.height - 150,
                        width: barrierWidth,
                        height: barrierHeight,
                        blocks: []
                    };
                    
                    // Create barrier blocks
                    const blockSize = 5;
                    for (let by = 0; by < barrierHeight; by += blockSize) {
                        for (let bx = 0; bx < barrierWidth; bx += blockSize) {
                            // Create barrier shape (arch-like)
                            const centerX = barrierWidth / 2;
                            const distFromCenter = Math.abs(bx - centerX);
                            const archHeight = barrierHeight - (distFromCenter * distFromCenter) / 100;
                            
                            if (by < archHeight) {
                                barrier.blocks.push({
                                    x: barrier.x + bx,
                                    y: barrier.y + by,
                                    width: blockSize,
                                    height: blockSize,
                                    destroyed: false
                                });
                            }
                        }
                    }
                    
                    this.barriers.push(barrier);
                }
            }
            
            update(deltaTime) {
                if (!this.isRunning || this.isPaused) return;
                
                // Move player
                if (this.keys['ArrowLeft'] && this.player.x > 0) {
                    this.player.x -= this.player.speed * deltaTime;
                }
                if (this.keys['ArrowRight'] && this.player.x < this.canvas.width - this.player.width) {
                    this.player.x += this.player.speed * deltaTime;
                }
                
                // Update bullets
                this.updateBullets(deltaTime);
                
                // Update enemies
                this.updateEnemies(deltaTime);
                
                // Enemy shooting
                this.handleEnemyShooting();
                
                // Check collisions
                this.checkCollisions();
                
                // Check wave completion
                if (this.enemies.length === 0) {
                    this.nextWave();
                }
                
                this.updateDisplay();
            }
            
            updateBullets(deltaTime) {
                // Update player bullets
                this.bullets = this.bullets.filter(bullet => {
                    bullet.y -= bullet.speed * deltaTime;
                    return bullet.y > -bullet.height;
                });
                
                // Update enemy bullets
                this.enemyBullets = this.enemyBullets.filter(bullet => {
                    bullet.y += bullet.speed * deltaTime;
                    return bullet.y < this.canvas.height;
                });
            }
            
            updateEnemies(deltaTime) {
                if (this.enemies.length === 0) return;
                
                // Check if enemies need to change direction
                let changeDirection = false;
                this.enemies.forEach(enemy => {
                    if ((this.enemyDirection > 0 && enemy.x + enemy.width >= this.canvas.width) ||
                        (this.enemyDirection < 0 && enemy.x <= 0)) {
                        changeDirection = true;
                    }
                });
                
                if (changeDirection) {
                    this.enemyDirection *= -1;
                    this.enemies.forEach(enemy => {
                        enemy.y += this.enemyDropDistance;
                    });
                    this.enemySpeed += 10; // Increase speed each direction change
                }
                
                // Move enemies
                this.enemies.forEach(enemy => {
                    enemy.x += this.enemySpeed * this.enemyDirection * deltaTime;
                });
                
                // Check if enemies reached player
                const lowestEnemy = Math.max(...this.enemies.map(e => e.y));
                if (lowestEnemy + 30 >= this.player.y) {
                    this.gameOver();
                }
            }
            
            handleEnemyShooting() {
                if (Date.now() - this.lastEnemyShot > this.enemyShootInterval) {
                    // Random enemy shoots
                    if (this.enemies.length > 0) {
                        const shooter = this.enemies[Math.floor(Math.random() * this.enemies.length)];
                        this.enemyBullets.push({
                            x: shooter.x + shooter.width / 2 - 2,
                            y: shooter.y + shooter.height,
                            width: 4,
                            height: 10,
                            speed: 200,
                            color: '#FF0000'
                        });
                        this.lastEnemyShot = Date.now();
                    }
                }
            }
            
            checkCollisions() {
                // Player bullets vs enemies
                this.bullets.forEach((bullet, bulletIndex) => {
                    this.enemies.forEach((enemy, enemyIndex) => {
                        if (this.isColliding(bullet, enemy)) {
                            this.addScore(enemy.points);
                            this.bullets.splice(bulletIndex, 1);
                            this.enemies.splice(enemyIndex, 1);
                            
                            // Chance to drop power-up
                            if (Math.random() < 0.1) {
                                this.powerUps.push({
                                    x: enemy.x,
                                    y: enemy.y,
                                    width: 20,
                                    height: 20,
                                    type: 'rapidfire',
                                    speed: 100
                                });
                            }
                        }
                    });
                });
                
                // Player bullets vs barriers
                this.bullets.forEach((bullet, bulletIndex) => {
                    this.barriers.forEach(barrier => {
                        barrier.blocks.forEach(block => {
                            if (!block.destroyed && this.isColliding(bullet, block)) {
                                block.destroyed = true;
                                this.bullets.splice(bulletIndex, 1);
                            }
                        });
                    });
                });
                
                // Enemy bullets vs player
                this.enemyBullets.forEach((bullet, bulletIndex) => {
                    if (this.isColliding(bullet, this.player)) {
                        this.lives--;
                        this.enemyBullets.splice(bulletIndex, 1);
                        
                        if (this.lives <= 0) {
                            this.gameOver();
                        }
                    }
                });
                
                // Enemy bullets vs barriers
                this.enemyBullets.forEach((bullet, bulletIndex) => {
                    this.barriers.forEach(barrier => {
                        barrier.blocks.forEach(block => {
                            if (!block.destroyed && this.isColliding(bullet, block)) {
                                block.destroyed = true;
                                this.enemyBullets.splice(bulletIndex, 1);
                            }
                        });
                    });
                });
                
                // Player vs power-ups
                this.powerUps = this.powerUps.filter(powerUp => {
                    powerUp.y += powerUp.speed * this.deltaTime;
                    
                    if (this.isColliding(powerUp, this.player)) {
                        this.activatePowerUp(powerUp.type);
                        return false;
                    }
                    
                    return powerUp.y < this.canvas.height;
                });
            }
            
            isColliding(rect1, rect2) {
                return rect1.x < rect2.x + rect2.width &&
                       rect1.x + rect1.width > rect2.x &&
                       rect1.y < rect2.y + rect2.height &&
                       rect1.y + rect1.height > rect2.y;
            }
            
            activatePowerUp(type) {
                switch(type) {
                    case 'rapidfire':
                        // Temporary rapid fire - implementation would need timer system
                        this.addScore(100);
                        break;
                }
            }
            
            nextWave() {
                this.wave++;
                this.enemySpeed = 50 + (this.wave - 1) * 10;
                this.enemyShootInterval = Math.max(500, 2000 - (this.wave - 1) * 100);
                this.addScore(this.wave * 100);
                
                setTimeout(() => {
                    this.initWave();
                }, 2000);
            }
            
            render() {
                this.clear();
                
                // Draw starfield background
                this.ctx.fillStyle = '#FFFFFF';
                for (let i = 0; i < 50; i++) {
                    const x = (i * 137) % this.canvas.width;
                    const y = (i * 211) % this.canvas.height;
                    this.ctx.fillRect(x, y, 1, 1);
                }
                
                // Draw enemies
                this.enemies.forEach(enemy => {
                    this.ctx.fillStyle = enemy.color;
                    this.ctx.fillRect(enemy.x, enemy.y, enemy.width, enemy.height);
                    
                    // Enemy symbol based on type
                    this.ctx.font = '20px Arial';
                    this.ctx.textAlign = 'center';
                    let symbol = '🧞‍♂️';
                    if (enemy.type === 'commander') symbol = '👑';
                    else if (enemy.type === 'warrior') symbol = '⚔️';
                    
                    this.ctx.fillText(symbol, enemy.x + enemy.width/2, enemy.y + 20);
                });
                
                // Draw barriers
                this.barriers.forEach(barrier => {
                    barrier.blocks.forEach(block => {
                        if (!block.destroyed) {
                            this.ctx.fillStyle = '#8B4513';
                            this.ctx.fillRect(block.x, block.y, block.width, block.height);
                        }
                    });
                });
                
                // Draw player
                this.ctx.fillStyle = '#FFD700';
                this.ctx.fillRect(this.player.x, this.player.y, this.player.width, this.player.height);
                
                // Flying carpet symbol
                this.ctx.font = '25px Arial';
                this.ctx.textAlign = 'center';
                this.ctx.fillText('🧞‍♂️', this.player.x + this.player.width/2, this.player.y + 20);
                
                // Draw bullets
                this.bullets.forEach(bullet => {
                    this.ctx.fillStyle = bullet.color;
                    this.ctx.fillRect(bullet.x, bullet.y, bullet.width, bullet.height);
                });
                
                this.enemyBullets.forEach(bullet => {
                    this.ctx.fillStyle = bullet.color;
                    this.ctx.fillRect(bullet.x, bullet.y, bullet.width, bullet.height);
                });
                
                // Draw power-ups
                this.powerUps.forEach(powerUp => {
                    this.ctx.fillStyle = '#00FFFF';
                    this.ctx.fillRect(powerUp.x, powerUp.y, powerUp.width, powerUp.height);
                    
                    this.ctx.font = '16px Arial';
                    this.ctx.textAlign = 'center';
                    this.ctx.fillText('⚡', powerUp.x + powerUp.width/2, powerUp.y + 12);
                });
                
                // Game over screen
                if (!this.isRunning) {
                    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
                    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
                    
                    this.drawText('Game Over!', this.canvas.width / 2, this.canvas.height / 2 - 40, {
                        font: '36px Orbitron',
                        color: '#FFD700',
                        align: 'center',
                        baseline: 'middle'
                    });
                    
                    this.drawText(`Wave Reached: ${this.wave}`, this.canvas.width / 2, this.canvas.height / 2, {
                        font: '24px Orbitron',
                        color: '#FFF',
                        align: 'center',
                        baseline: 'middle'
                    });
                    
                    this.drawText(`Final Score: ${this.score}`, this.canvas.width / 2, this.canvas.height / 2 + 40, {
                        font: '24px Orbitron',
                        color: '#FFF',
                        align: 'center',
                        baseline: 'middle'
                    });
                }
            }
            
            updateWaveInfo() {
                const waveNames = [
                    'Desert Scouts', 'Oasis Raiders', 'Dune Warriors', 'Mirage Masters',
                    'Sandstorm Legion', 'Atlas Guardians', 'Mystical Horde', 'Final Invasion'
                ];
                
                const waveName = waveNames[Math.min(this.wave - 1, waveNames.length - 1)];
                document.getElementById('wave-status').textContent = `Wave ${this.wave} - ${waveName}`;
                document.getElementById('enemies-left').textContent = this.enemies.length;
            }
            
            gameOver() {
                this.stop();
                
                const gameData = {
                    score: this.score,
                    level: this.wave,
                    playTime: Date.now() - this.startTime
                };
                
                scoreManager.updatePlayerStats('atlas-invaders', gameData);
                
                if (scoreManager.setHighScore('atlas-invaders', this.score)) {
                    setTimeout(() => {
                        alert('🎉 New High Score! 🎉');
                    }, 100);
                }
            }
            
            resetGame() {
                this.stop();
                this.resetScore();
                this.wave = 1;
                this.lives = 3;
                this.player.x = this.canvas.width / 2 - 25;
                this.enemySpeed = 50;
                this.enemyShootInterval = 2000;
                this.bullets = [];
                this.enemyBullets = [];
                this.powerUps = [];
                this.initWave();
                this.updateDisplay();
            }
            
            onStart() {
                this.startTime = Date.now();
                this.lastPlayerShot = 0;
                this.lastEnemyShot = 0;
                document.getElementById('startBtn').textContent = '🛸 Defending...';
            }
            
            onStop() {
                document.getElementById('startBtn').textContent = '🎮 Start Defense';
            }
            
            updateDisplay() {
                document.getElementById('current-score').textContent = this.score;
                document.getElementById('high-score').textContent = this.highScore;
                document.getElementById('wave').textContent = this.wave;
                document.getElementById('lives').textContent = this.lives;
                document.getElementById('enemies-left').textContent = this.enemies.length;
            }
        }
        
        const game = new AtlasInvaders();
    </script>
</body>
</html>
